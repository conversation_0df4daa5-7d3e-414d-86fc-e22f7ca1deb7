package com.dataforge.security.validator;

/**
 * 验证结果封装
 * 统一验证结果格式，支持链式验证
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
public class ValidationResult {
    
    private final boolean valid;
    private final String message;
    
    private ValidationResult(boolean valid, String message) {
        this.valid = valid;
        this.message = message;
    }
    
    public static ValidationResult valid() {
        return new ValidationResult(true, null);
    }
    
    public static ValidationResult invalid(String message) {
        return new ValidationResult(false, message);
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public boolean isInvalid() {
        return !valid;
    }
    
    public String getMessage() {
        return message;
    }
    
    @Override
    public String toString() {
        return valid ? "VALID" : "INVALID: " + message;
    }
}
