package com.dataforge.security;

import lombok.Builder;
import lombok.Getter;

/**
 * 安全策略配置实体
 * 定义系统运行的安全边界
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
@Getter
@Builder
public class SecurityPolicy {
    
    // 脚本执行限制
    private final long maxScriptExecutionTime; // 毫秒
    private final long maxMemoryUsage; // 字节
    
    // 输入验证
    private final boolean enableInputValidation;
    private final int maxStringLength;
    private final int maxCollectionSize;
    
    // 数据保护
    private final boolean enableSensitiveDataMasking;
    private final boolean enableSqlInjectionCheck;
    private final boolean enableXssProtection;
    
    // 文件系统限制
    private final boolean enableFileAccessRestriction;
    private final long maxFileSize;
    
    // 网络限制
    private final boolean enableNetworkRestriction;
    
    /**
     * 获取默认安全策略
     */
    public static SecurityPolicy getDefault() {
        return SecurityPolicy.builder()
            .maxScriptExecutionTime(100)
            .maxMemoryUsage(10 * 1024 * 1024)
            .enableInputValidation(true)
            .maxStringLength(10000)
            .maxCollectionSize(100000)
            .enableSensitiveDataMasking(true)
            .enableSqlInjectionCheck(true)
            .enableXssProtection(true)
            .enableFileAccessRestriction(true)
            .maxFileSize(100 * 1024 * 1024) // 100MB
            .enableNetworkRestriction(true)
            .build();
    }
}
