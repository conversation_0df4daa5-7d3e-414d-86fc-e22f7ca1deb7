package com.dataforge.generators;

import com.dataforge.core.GeneratorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import java.util.Set;
import java.util.HashSet;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class EnterpriseInfoGeneratorTest {

    private EnterpriseInfoGenerator enterpriseInfoGenerator;
    private GeneratorContext mockContext;

    @BeforeEach
    void setUp() {
        enterpriseInfoGenerator = new EnterpriseInfoGenerator();
        mockContext = mock(GeneratorContext.class);
        when(mockContext.getSeed()).thenReturn(12345L);
    }

    @Test
    void testSupports() {
        assertTrue(enterpriseInfoGenerator.supports("enterprise"));
        assertTrue(enterpriseInfoGenerator.supports("ENTERPRISE_INFO"));
        assertFalse(enterpriseInfoGenerator.supports("invalid_type"));
    }

    @Test
    void testEnterpriseInfoFormat() {
        String enterpriseInfo = enterpriseInfoGenerator.generate(mockContext);
        
        assertNotNull(enterpriseInfo);
        // 验证JSON格式
        assertTrue(enterpriseInfo.startsWith("{") && enterpriseInfo.endsWith("}"));
        
        // 验证包含必要字段
        assertTrue(enterpriseInfo.contains("\"name\""));
        assertTrue(enterpriseInfo.contains("\"unifiedSocialCreditCode\""));
        assertTrue(enterpriseInfo.contains("\"organizationCode\""));
        assertTrue(enterpriseInfo.contains("\"leiCode\""));
    }

    @Test
    void testFieldFormats() {
        String enterpriseInfo = enterpriseInfoGenerator.generate(mockContext);
        
        // 提取各字段值进行验证
        String name = extractJsonField(enterpriseInfo, "name");
        String creditCode = extractJsonField(enterpriseInfo, "unifiedSocialCreditCode");
        String orgCode = extractJsonField(enterpriseInfo, "organizationCode");
        String leiCode = extractJsonField(enterpriseInfo, "leiCode");

        // 验证企业名称
        assertNotNull(name);
        assertTrue(name.matches("^[\\u4e00-\\u9fa5（）()]{5,50}$"));

        // 验证统一社会信用代码
        assertNotNull(creditCode);
        assertEquals(18, creditCode.length());
        assertTrue(creditCode.matches("^[0-9A-Z]{18}$"));

        // 验证组织机构代码
        assertNotNull(orgCode);
        assertEquals(9, orgCode.length());
        assertTrue(orgCode.matches("^[0-9A-Z]{8}[0-9A-Z]$"));

        // 验证LEI码
        assertNotNull(leiCode);
        assertEquals(20, leiCode.length());
        assertTrue(leiCode.matches("^[0-9A-Z]{20}$"));
    }

    @ParameterizedTest
    @ValueSource(longs = {1, 100, 9999})
    void testGenerateWithDifferentSeeds(long seed) {
        when(mockContext.getSeed()).thenReturn(seed);
        String enterpriseInfo = enterpriseInfoGenerator.generate(mockContext);
        assertNotNull(enterpriseInfo);
        assertTrue(enterpriseInfo.startsWith("{"));
    }

    @Test
    void testCompanyNameDiversity() {
        Set<String> names = new HashSet<>();
        for (int i = 0; i < 100; i++) {
            when(mockContext.getSeed()).thenReturn(System.currentTimeMillis() + i);
            String name = extractJsonField(
                enterpriseInfoGenerator.generate(mockContext), 
                "name"
            );
            names.add(name);
        }
        assertTrue(names.size() > 80, "企业名称多样性不足");
    }

    @Test
    void testCreditCodeCheckDigit() {
        String creditCode = extractJsonField(
            enterpriseInfoGenerator.generate(mockContext),
            "unifiedSocialCreditCode"
        );
        assertTrue(validateCreditCodeCheckDigit(creditCode), 
            "统一社会信用代码校验位不正确");
    }

    @Test
    void testJsonFieldCompleteness() {
        String json = enterpriseInfoGenerator.generate(mockContext);
        assertAll("验证JSON字段完整性",
            () -> assertNotNull(extractJsonField(json, "name")),
            () -> assertNotNull(extractJsonField(json, "unifiedSocialCreditCode")),
            () -> assertNotNull(extractJsonField(json, "organizationCode")),
            () -> assertNotNull(extractJsonField(json, "leiCode")),
            () -> assertNotNull(extractJsonField(json, "registrationDate")),
            () -> assertNotNull(extractJsonField(json, "businessScope"))
        );
    }

    private boolean validateCreditCodeCheckDigit(String creditCode) {
        if (creditCode == null || creditCode.length() != 18) {
            return false;
        }
        // 统一社会信用代码校验位算法实现
        int[] weights = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};
        char[] chars = creditCode.toCharArray();
        int sum = 0;
        
        for (int i = 0; i < 17; i++) {
            char c = chars[i];
            int value = Character.isDigit(c) ? c - '0' : c - 'A' + 10;
            sum += value * weights[i];
        }
        
        int checkDigit = 31 - (sum % 31);
        char expected = checkDigit == 31 ? '0' : 
                       checkDigit < 10 ? (char)(checkDigit + '0') : 
                       (char)(checkDigit - 10 + 'A');
        
        return chars[17] == expected;
    }

    private String extractJsonField(String json, String fieldName) {
        String pattern = "\"" + fieldName + "\":\"([^\"]+)\"";
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(json);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }
}
