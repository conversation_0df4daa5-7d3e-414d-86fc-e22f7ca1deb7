package com.dataforge.generators;

import com.dataforge.core.GeneratorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AddressGeneratorTest {

    private AddressGenerator addressGenerator;
    private GeneratorContext mockContext;

    @BeforeEach
    void setUp() {
        addressGenerator = new AddressGenerator();
        mockContext = mock(GeneratorContext.class);
        when(mockContext.getSeed()).thenReturn(12345L);
    }

    @Test
    void testSupports() {
        assertTrue(addressGenerator.supports("address"));
        assertTrue(addressGenerator.supports("ADDRESS"));
        assertFalse(addressGenerator.supports("invalid_type"));
    }

    @Test
    void testGenerateReturnsCompleteAddress() {
        Map<String, String> address = addressGenerator.generate(mockContext);
        
        assertNotNull(address);
        assertAll(
            () -> assertNotNull(address.get("province")),
            () -> assertNotNull(address.get("city")),
            () -> assertNotNull(address.get("district")),
            () -> assertNotNull(address.get("detail")),
            () -> assertNotNull(address.get("postcode"))
        );
    }

    @Test
    void testPostcodeFormat() {
        Map<String, String> address = addressGenerator.generate(mockContext);
        String postcode = address.get("postcode");
        
        assertNotNull(postcode);
        assertEquals(6, postcode.length());
        assertTrue(postcode.matches("\\d{6}"));
    }

    @Test
    void testAddressComponentsValid() {
        Map<String, String> address = addressGenerator.generate(mockContext);
        
        // 验证省份是否在配置中
        assertTrue(
            address.get("province").equals("北京市") ||
            address.get("province").equals("上海市") ||
            address.get("province").equals("广东省")
        );
        
        // 验证详细地址格式
        assertTrue(address.get("detail").matches(".+\\d+号.+"));
    }
}
