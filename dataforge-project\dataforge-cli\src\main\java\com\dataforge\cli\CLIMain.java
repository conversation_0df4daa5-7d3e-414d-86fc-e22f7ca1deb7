package com.dataforge.cli;

import com.dataforge.config.ConfigLoader;
import com.dataforge.config.GlobalConfig;
import org.apache.commons.cli.*;

import java.io.FileNotFoundException;

public class CLIMain {
    public static void main(String[] args) {
        Options options = new Options();
        
        // 定义命令行选项
        options.addOption("c", "config", true, "Path to configuration file");
        options.addOption("f", "format", true, "Output format (csv, json, sql)");
        options.addOption("o", "output", true, "Output directory path");
        options.addOption("b", "batch-size", true, "Batch size for data generation");
        options.addOption("h", "help", false, "Print help information");
        
        CommandLineParser parser = new DefaultParser();
        try {
            // 解析命令行参数
            CommandLine cmd = parser.parse(options, args);
            
            if (cmd.hasOption("h")) {
                printHelp(options);
                System.exit(0);
            }
            
            GlobalConfig config;
            if (cmd.hasOption("c")) {
                // 从配置文件加载
                config = ConfigLoader.loadConfig(cmd.getOptionValue("c"));
            } else {
                // 使用默认配置
                config = ConfigLoader.loadDefaultConfig();
            }
            
            // 覆盖配置文件的命令行参数
            if (cmd.hasOption("f")) {
                config.setOutputFormat(cmd.getOptionValue("f"));
            }
            if (cmd.hasOption("o")) {
                config.setOutputPath(cmd.getOptionValue("o"));
            }
            if (cmd.hasOption("b")) {
                config.setBatchSize(Integer.parseInt(cmd.getOptionValue("b")));
            }
            
            System.out.println("Using configuration: " + config);
            // 这里将添加数据生成逻辑
            
        } catch (ParseException e) {
            System.err.println("Error parsing command line: " + e.getMessage());
            printHelp(options);
            System.exit(1);
        } catch (FileNotFoundException e) {
            System.err.println("Configuration file not found: " + e.getMessage());
            System.exit(1);
        } catch (NumberFormatException e) {
            System.err.println("Invalid batch size: " + e.getMessage());
            System.exit(1);
        }
    }
    
    private static void printHelp(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.printHelp("dataforge-cli", options);
    }
}
