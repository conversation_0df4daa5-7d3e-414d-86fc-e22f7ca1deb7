package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;
import com.dataforge.config.ConfigLoader;
import org.yaml.snakeyaml.Yaml;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 地址数据生成器，支持生成中国省市区三级联动地址
 * 从address-config.yaml加载配置数据
 */
public class AddressGenerator implements DataGenerator<Map<String, String>> {

    private static final String CONFIG_PATH = "dataforge-project/address-config.yaml";
    private static Map<String, Object> addressConfig;
    private Random random;

    static {
        try {
            Yaml yaml = new Yaml();
            InputStream inputStream = Files.newInputStream(Paths.get(CONFIG_PATH));
            addressConfig = yaml.load(inputStream);
        } catch (Exception e) {
            throw new RuntimeException("加载地址配置失败", e);
        }
    }

    @Override
    public Map<String, String> generate(GeneratorContext context) {
        this.random = new Random(context.getSeed());
        Map<String, String> address = new HashMap<>();
        
        // 从配置获取省份列表
        List<Map<String, Object>> provinces = (List<Map<String, Object>>) addressConfig.get("provinces");
        Map<String, Object> provinceInfo = provinces.get(random.nextInt(provinces.size()));
        String province = (String) provinceInfo.get("name");
        address.put("province", province);
        
        // 从配置获取城市列表
        List<Map<String, Object>> cities = (List<Map<String, Object>>) provinceInfo.get("cities");
        Map<String, Object> cityInfo = cities.get(random.nextInt(cities.size()));
        String city = (String) cityInfo.get("name");
        address.put("city", city);
        
        // 从配置获取区县
        List<String> districts = (List<String>) cityInfo.get("districts");
        String district = districts.get(random.nextInt(districts.size()));
        address.put("district", district);
        
        // 生成详细地址
        List<String> streets = (List<String>) addressConfig.get("streets");
        List<String> communities = (List<String>) addressConfig.get("communities");
        address.put("detail", streets.get(random.nextInt(streets.size())) + 
            (random.nextInt(100) + 1) + "号" + 
            communities.get(random.nextInt(communities.size())));
        
        // 生成邮编(使用配置中的省份代码)
        address.put("postcode", generatePostcode(provinceInfo));
        
        return address;
    }

    @Override
    public boolean supports(String type) {
        return "address".equalsIgnoreCase(type);
    }

    private String generatePostcode(Map<String, Object> provinceInfo) {
        // 使用配置中的省份代码作为邮编前缀
        int provinceCode = (int) provinceInfo.get("code");
        return String.format("%02d", provinceCode) + (1000 + random.nextInt(9000));
    }
}
