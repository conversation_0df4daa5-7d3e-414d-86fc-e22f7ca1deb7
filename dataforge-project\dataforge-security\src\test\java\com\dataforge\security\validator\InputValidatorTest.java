package com.dataforge.security.validator;

import com.dataforge.security.SecurityPolicy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 输入验证器测试
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
class InputValidatorTest {
    
    private InputValidator validator;
    
    @BeforeEach
    void setUp() {
        SecurityPolicy policy = SecurityPolicy.getDefault();
        validator = new InputValidator(policy);
    }
    
    @Test
    void testValidStringInput() {
        ValidationResult result = validator.validateString("hello world", "test");
        assertTrue(result.isValid());
    }
    
    @Test
    void testNullStringInput() {
        ValidationResult result = validator.validateString(null, "test");
        assertTrue(result.isValid());
    }
    
    @Test
    void testSqlInjectionDetection() {
        ValidationResult result = validator.validateString(
            "'; DROP TABLE users; --", 
            "username"
        );
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("SQL注入"));
    }
    
    @Test
    void testXssAttackDetection() {
        ValidationResult result = validator.validateString(
            "<script>alert('xss')</script>", 
            "content"
        );
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("XSS"));
    }
    
    @Test
    void testStringLengthLimit() {
        String longString = "a".repeat(10001);
        ValidationResult result = validator.validateString(longString, "test");
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("长度超过限制"));
    }
    
    @Test
    void testValidCollection() {
        List<String> validList = Arrays.asList("item1", "item2", "item3");
        ValidationResult result = validator.validateCollection(validList, "items");
        assertTrue(result.isValid());
    }
    
    @Test
    void testCollectionSizeLimit() {
        List<String> largeList = Arrays.asList(new String[100001]);
        ValidationResult result = validator.validateCollection(largeList, "items");
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("大小超过限制"));
    }
    
    @Test
    void testNumberRangeValidation() {
        ValidationResult result = validator.validateNumberRange(50, "age", 0, 100);
        assertTrue(result.isValid());
        
        result = validator.validateNumberRange(150, "age", 0, 100);
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("超出范围"));
    }
    
    @Test
    void testFilePathValidation() {
        ValidationResult result = validator.validateFilePath("data/file.txt", "path");
        assertTrue(result.isValid());
        
        result = validator.validateFilePath("../etc/passwd", "path");
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("非法字符"));
        
        result = validator.validateFilePath("/etc/passwd", "path");
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("绝对路径"));
    }
}
