# DataForge Security Module

## 安全加固模块 - 核心功能概览

### 🛡️ 核心安全能力

#### 1. 输入验证与防护
- **SQL注入防护**: 基于正则表达式的SQL注入检测
- **XSS攻击防护**: 跨站脚本攻击检测与过滤
- **输入长度限制**: 防止缓冲区溢出攻击
- **文件路径验证**: 防止目录遍历攻击

#### 2. 敏感数据脱敏
- **身份证号**: 保留前6位和后4位 (110105********1234)
- **银行卡号**: 保留前6位和后4位 (622202****1234)
- **手机号**: 保留前3位和后4位 (138****1234)
- **邮箱**: 保留前3位和域名 (abc***@example.com)
- **姓名**: 保留姓氏 (张*)

#### 3. Groovy脚本安全沙箱
- **白名单类限制**: 只允许基础类型和常用工具类
- **黑名单类禁止**: 禁止System、Runtime、File等危险类
- **执行时间限制**: 默认100ms超时保护
- **内存使用限制**: 默认10MB内存上限
- **脚本缓存**: 提升重复脚本执行性能

### 🔧 使用示例

#### 输入验证
```java
SecurityPolicy policy = SecurityPolicy.getDefault();
InputValidator validator = new InputValidator(policy);

// 验证字符串
ValidationResult result = validator.validateString(userInput, "username");
if (result.isInvalid()) {
    throw new SecurityException(result.getMessage());
}

// 验证集合
result = validator.validateCollection(dataList, "userList");
```

#### 数据脱敏
```java
// 脱敏身份证号
String maskedId = SensitiveDataMasker.maskIdCard("110105199003078888");
// 输出: 110105********8888

// 通用脱敏
String masked = SensitiveDataMasker.mask(email, SensitiveDataMasker.DataType.EMAIL);
```

#### 安全脚本执行
```java
GroovySandbox sandbox = new GroovySandbox();
try {
    Object result = sandbox.executeScript(
        "input.toUpperCase()", 
        "hello world"
    );
    System.out.println(result); // HELLO WORLD
} finally {
    sandbox.shutdown();
}
```

### ⚙️ 配置参数

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| maxScriptExecutionTime | 100ms | 脚本最大执行时间 |
| maxMemoryUsage | 10MB | 脚本最大内存使用 |
| maxStringLength | 10,000 | 字符串最大长度 |
| maxCollectionSize | 100,000 | 集合最大元素数量 |
| enableInputValidation | true | 是否启用输入验证 |
| enableSensitiveDataMasking | true | 是否启用数据脱敏 |

### 🚨 安全警告

1. **永远不要**在生产环境中禁用输入验证
2. **始终**对敏感数据进行脱敏处理
3. **定期**更新安全策略配置
4. **监控**脚本执行日志，及时发现异常

### 📊 性能指标

- 输入验证延迟: < 1ms
- 数据脱敏延迟: < 0.5ms
- 脚本执行延迟: < 100ms (含安全检查)
- 内存占用: < 50MB (完整模块)

### 🔍 安全审计

所有安全相关操作都会记录详细日志，包括：
- 输入验证失败记录
- 敏感数据访问记录
- 脚本执行异常记录
- 安全策略违规记录
