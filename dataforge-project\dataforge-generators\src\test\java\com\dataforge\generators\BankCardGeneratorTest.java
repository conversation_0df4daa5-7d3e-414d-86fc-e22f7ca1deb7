package com.dataforge.generators;

import com.dataforge.core.GeneratorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class BankCardGeneratorTest {

    private BankCardGenerator bankCardGenerator;
    private GeneratorContext mockContext;

    @BeforeEach
    void setUp() {
        bankCardGenerator = new BankCardGenerator();
        mockContext = mock(GeneratorContext.class);
        when(mockContext.getSeed()).thenReturn(12345L);
    }

    @Test
    void testSupports() {
        assertTrue(bankCardGenerator.supports("bankcard"));
        assertTrue(bankCardGenerator.supports("BANK_CARD"));
        assertFalse(bankCardGenerator.supports("invalid_type"));
    }

    @Test
    void testCardNumberFormat() {
        String cardNumber = bankCardGenerator.generate(mockContext);
        
        assertNotNull(cardNumber);
        assertTrue(cardNumber.matches("^\\d{16,19}$"));
    }

    @Test
    void testLuhnCheckDigit() {
        String cardNumber = bankCardGenerator.generate(mockContext);
        
        // 验证Luhn算法校验位
        int sum = 0;
        boolean alternate = false;
        for (int i = cardNumber.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(cardNumber.charAt(i));
            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = (digit % 10) + 1;
                }
            }
            sum += digit;
            alternate = !alternate;
        }
        assertTrue(sum % 10 == 0);
    }

    @Test
    void testCardPrefixValid() {
        String cardNumber = bankCardGenerator.generate(mockContext);
        String prefix = cardNumber.substring(0, 6);
        
        // 验证卡号前缀是否在常见范围内
        assertTrue(
            prefix.startsWith("4") || // Visa
            prefix.startsWith("5") || // MasterCard
            prefix.startsWith("62") || // UnionPay
            prefix.startsWith("34") || // AmEx
            prefix.startsWith("37")    // AmEx
        );
    }

    @Test
    void testCardLengthValid() {
        String cardNumber = bankCardGenerator.generate(mockContext);
        
        // 验证卡号长度是否合理
        assertTrue(cardNumber.length() >= 16 && cardNumber.length() <= 19);
    }
}
