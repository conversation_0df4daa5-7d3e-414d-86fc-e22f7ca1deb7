package com.dataforge.validators;

import com.dataforge.core.ValidationResult;
import com.dataforge.security.SecurityPolicy;
import com.dataforge.security.sandbox.GroovySandbox;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class GroovyValidatorTest {

    private SecurityPolicy createTestPolicy() {
        SecurityPolicy policy = new SecurityPolicy();
        policy.setMaxExecutionTime(100); // 100ms超时
        policy.setMaxMemoryUsage(10 * 1024 * 1024); // 10MB内存限制
        return policy;
    }

    @Test
    void testSimpleValidation() {
        SecurityPolicy policy = createTestPolicy();
        GroovyValidator validator = new GroovyValidator("age_check", "value > 18", policy);
        
        // 测试大于18的值
        ValidationResult result = validator.validate(20);
        assertTrue(result.isValid());
        assertEquals("age_check", result.getRule());
        
        // 测试小于18的值
        result = validator.validate(16);
        assertFalse(result.isValid());
        assertEquals("校验规则 'age_check' 验证失败", result.getMessage());
    }

    @Test
    void testNonBooleanReturn() {
        SecurityPolicy policy = createTestPolicy();
        GroovyValidator validator = new GroovyValidator("invalid_script", "'hello'", policy);
        
        ValidationResult result = validator.validate(123);
        assertFalse(result.isValid());
        assertEquals("脚本必须返回布尔值", result.getMessage());
    }

    @Test
    void testExceptionInScript() {
        SecurityPolicy policy = createTestPolicy();
        GroovyValidator validator = new GroovyValidator("error_script", "throw new Exception('test exception')", policy);
        
        ValidationResult result = validator.validate(123);
        assertFalse(result.isValid());
        assertTrue(result.getMessage().startsWith("脚本执行错误: test exception"));
    }
}