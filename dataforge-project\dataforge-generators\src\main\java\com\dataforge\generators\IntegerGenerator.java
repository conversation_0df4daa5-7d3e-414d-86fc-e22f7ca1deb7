package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;

import java.util.Random;

public class IntegerGenerator implements DataGenerator<Integer> {
    
    @Override
    public Integer generate(GeneratorContext context) {
        Random random = new Random(context.getSeed());
        return random.nextInt();
    }
    
    @Override
    public boolean supports(String type) {
        return "integer".equalsIgnoreCase(type);
    }
}
