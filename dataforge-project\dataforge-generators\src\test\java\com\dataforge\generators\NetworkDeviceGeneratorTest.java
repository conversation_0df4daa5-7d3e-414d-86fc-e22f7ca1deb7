package com.dataforge.generators;

import com.dataforge.core.GeneratorContext;
import org.junit.jupiter.api.Test;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;
import java.util.regex.Pattern;

class NetworkDeviceGeneratorTest {
    // IPv6地址正则表达式
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$");

    // IMEI号码正则表达式(15位数字)
    private static final Pattern IMEI_PATTERN = Pattern.compile(
        "^[0-9]{15}$");
    private final NetworkDeviceGenerator generator = new NetworkDeviceGenerator();
    private final GeneratorContext context = new GeneratorContext(Map.of(), System.currentTimeMillis());

    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}" +
        "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    // MAC地址正则表达式
    private static final Pattern MAC_PATTERN = Pattern.compile(
        "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$");

    @Test
    void testSupportsNetworkTypes() {
        assertTrue(generator.supports("network"));
        assertTrue(generator.supports("ip"));
        assertTrue(generator.supports("mac"));
        assertFalse(generator.supports("invalid"));
    }

    @Test
    void testGenerateIpAddressFormat() {
        for (int i = 0; i < 100; i++) {
            String ip = generator.generate(context);
            if (ip.contains(".")) { // 是IP地址
                assertTrue(IP_PATTERN.matcher(ip).matches(), 
                    "Invalid IP address format: " + ip);
            }
        }
    }

    @Test
    void testGenerateMacAddressFormat() {
        for (int i = 0; i < 100; i++) {
            String mac = generator.generate(context);
            if (mac.contains(":")) { // 是MAC地址
                assertTrue(MAC_PATTERN.matcher(mac).matches(), 
                    "Invalid MAC address format: " + mac);
                // 验证本地管理位(第二位最低位为1)
                char secondChar = mac.charAt(1);
                int value = Integer.parseInt(String.valueOf(secondChar), 16);
                assertTrue((value & 0x02) == 0x02, 
                    "MAC address should be locally administered");
            }
        }
    }

    @Test
    void testGenerateIpv6AddressFormat() {
        GeneratorContext ipv6Context = new GeneratorContext(
            Map.of("network.type", "ipv6"), System.currentTimeMillis());
        
        for (int i = 0; i < 100; i++) {
            String ipv6 = generator.generate(ipv6Context);
            assertTrue(IPV6_PATTERN.matcher(ipv6).matches(),
                "Invalid IPv6 address format: " + ipv6);
        }
    }

    @Test
    void testGenerateImeiFormat() {
        GeneratorContext imeiContext = new GeneratorContext(
            Map.of("network.type", "imei"), System.currentTimeMillis());
        
        for (int i = 0; i < 100; i++) {
            String imei = generator.generate(imeiContext);
            assertTrue(IMEI_PATTERN.matcher(imei).matches(),
                "Invalid IMEI format: " + imei);
            assertTrue(isValidImei(imei),
                "IMEI failed Luhn check: " + imei);
        }
    }

    /**
     * 验证IMEI号码的Luhn校验位
     */
    private boolean isValidImei(String imei) {
        int sum = 0;
        for (int i = 0; i < 14; i++) {
            int digit = Character.getNumericValue(imei.charAt(i));
            if (i % 2 == 0) { // 偶数位(从0开始)
                sum += digit;
            } else { // 奇数位
                digit *= 2;
                sum += digit > 9 ? digit - 9 : digit;
            }
        }
        int checkDigit = Character.getNumericValue(imei.charAt(14));
        return (sum + checkDigit) % 10 == 0;
    }
}
