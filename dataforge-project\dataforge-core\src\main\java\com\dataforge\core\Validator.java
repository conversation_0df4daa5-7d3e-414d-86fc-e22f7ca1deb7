package com.dataforge.core;

/**
 * 数据校验器接口
 * 
 * <p>定义了数据校验的标准接口，支持各种类型的数据校验规则。
 * 实现类可以提供基于正则表达式、Groovy脚本或其他自定义逻辑的校验功能。</p>
 * 
 * <p>使用示例：
 * <pre>
 * Validator validator = new RegexValidator("email", "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$");
 * ValidationResult result = validator.validate("<EMAIL>");
 * if (result.isValid()) {
 *     // 校验通过
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 1.0
 */
public interface Validator {
    
    /**
     * 执行数据校验
     * 
     * @param value 待校验的数据对象
     * @return 校验结果，包含是否通过校验以及相关信息
     * @throws IllegalArgumentException 如果输入参数无效
     */
    ValidationResult validate(Object value);
    
    /**
     * 获取校验规则的标识符或描述
     * 
     * @return 校验规则的字符串表示，用于日志记录和错误信息
     */
    String getRule();
    
    /**
     * 检查此校验器是否支持指定类型的数据校验
     * 
     * @param dataType 数据类型标识
     * @return 如果支持返回true，否则返回false
     */
    default boolean supports(String dataType) {
        return true; // 默认支持所有类型
    }
    
    /**
     * 获取校验器的优先级
     * 数值越小优先级越高，用于多个校验器的排序
     * 
     * @return 优先级数值，默认为0
     */
    default int getPriority() {
        return 0;
    }
}
