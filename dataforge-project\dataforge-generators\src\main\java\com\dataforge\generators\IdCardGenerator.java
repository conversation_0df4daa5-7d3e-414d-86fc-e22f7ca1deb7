package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 中国居民身份证号码生成器，符合GB11643-1999标准。
 * 生成的身份证号码包含地区码、出生日期、顺序码和校验码。
 * <p>
 * 身份证号码结构：
 * 1-6位：地区码，表示户籍所在地的行政区划代码
 * 7-14位：出生日期，格式为YYYYMMDD
 * 15-17位：顺序码，同一地区同一天出生的人的编号，奇数为男性，偶数为女性
 * 18位：校验码，根据前17位计算得出，用于验证号码的有效性
 * </p>
 * <p>
 * 支持配置参数：
 * - startYear：出生年份范围的起始年（默认1950年）
 * - endYear：出生年份范围的结束年（默认2005年）
 * - useLunar：是否使用农历日期（默认false）
 * </p>
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 1.0
 */
public class IdCardGenerator implements DataGenerator<String> {
    private static final Logger log = LoggerFactory.getLogger(IdCardGenerator.class);

    /**
     * 常用省市地区码（前6位）
     * 包含了中国主要城市的区县级行政区划代码
     */
    private static final List<String> AREA_CODES = Arrays.asList(
        "110101", "110102", "110105", "110106", "110108", // 北京
        "310101", "310104", "310105", "310106", "310109", // 上海
        "440101", "440103", "440104", "440105", "440106", // 广州
        "440301", "440303", "440304", "440305", "440306", // 深圳
        "320501", "320503", "320505", "320506", "320507", // 苏州
        "330101", "330102", "330103", "330104", "330105", // 杭州
        "510101", "510104", "510105", "510106", "510107", // 成都
        "500101", "500102", "500103", "500104", "500105"  // 重庆
    );
    
    /**
     * 农历月份天数映射（大月30天，小月29天）
     * 用于生成农历日期时确定每月的天数
     */
    private static final int[] LUNAR_MONTH_DAYS = {29, 30};
    
    /**
     * 默认出生年份范围的起始年
     */
    private static final int DEFAULT_START_YEAR = 1950;
    
    /**
     * 默认出生年份范围的结束年
     */
    private static final int DEFAULT_END_YEAR = 2005;

    /**
     * 生成符合GB11643-1999标准的中国居民身份证号码。
     * <p>
     * 该方法根据提供的上下文配置生成一个有效的18位身份证号码，包括：
     * - 随机选择一个有效的地区码
     * - 根据配置的年份范围生成出生日期（支持公历和农历）
     * - 生成3位随机顺序码
     * - 根据前17位计算校验码
     * </p>
     *
     * @param context 生成器上下文，包含配置参数和随机种子
     * @return 18位身份证号码字符串
     * @throws IllegalArgumentException 如果context为null或配置参数无效
     */
    @Override
    public String generate(GeneratorContext context) {
        // 参数校验
        if (context == null) {
            throw new IllegalArgumentException("Context cannot be null");
        }
        
        // 获取配置参数
        Integer startYear = getValidStartYear(context);
        Integer endYear = getValidEndYear(context, startYear);
        Boolean useLunar = context.getConfig(CONFIG_USE_LUNAR, Boolean.class);
        if (useLunar == null) {
            useLunar = false;
        }
        
        // 获取性别配置（如果有）
        String gender = context.getConfig(CONFIG_GENDER, String.class);
        boolean isMale = true;  // 默认为男性
        boolean hasGenderConfig = false;
        
        if (gender != null) {
            if (GENDER_MALE.equalsIgnoreCase(gender)) {
                isMale = true;
                hasGenderConfig = true;
            } else if (GENDER_FEMALE.equalsIgnoreCase(gender)) {
                isMale = false;
                hasGenderConfig = true;
            } else {
                log.warn("Invalid gender configuration: {}, will use random gender", gender);
            }
        }

        // 创建随机数生成器
        Random random = new Random(context.getSeed());

        // 1. 随机选择一个地区码
        String areaCode = AREA_CODES.get(random.nextInt(AREA_CODES.size()));
        log.debug("Selected area code: {}", areaCode);

        // 2. 生成出生日期
        String birthDate = useLunar
            ? generateLunarDate(random, startYear, endYear)
            : generateGregorianDate(random, startYear, endYear);
        log.debug("Generated birth date: {}", birthDate);

        // 3. 生成顺序码（3位）
        int sequenceNumber;
        if (hasGenderConfig) {
            // 根据性别配置生成顺序码：男性为奇数，女性为偶数
            sequenceNumber = random.nextInt(499) * 2 + (isMale ? 1 : 0);
            log.debug("Generated sequence number based on gender ({}): {}",
                      isMale ? "male" : "female", sequenceNumber);
        } else {
            // 随机生成顺序码
            sequenceNumber = random.nextInt(999);
            log.debug("Generated random sequence number: {}", sequenceNumber);
        }
        
        String sequenceCode = String.format("%03d", sequenceNumber);

        // 4. 计算校验码
        String base = areaCode + birthDate + sequenceCode;
        char checksum = calculateChecksum(base);

        return base + checksum;
    }
    
    /**
     * 获取有效的起始年份
     *
     * @param context 生成器上下文
     * @return 有效的起始年份
     */
    private Integer getValidStartYear(GeneratorContext context) {
        Integer startYear = context.getConfig(CONFIG_START_YEAR, Integer.class);
        if (startYear == null) {
            log.debug("No start year configured, using default: {}", DEFAULT_START_YEAR);
            return DEFAULT_START_YEAR;
        }
        
        int currentYear = LocalDate.now().getYear();
        if (startYear < 1900 || startYear > currentYear) {
            log.warn("Invalid start year: {}, using default start year {}", startYear, DEFAULT_START_YEAR);
            return DEFAULT_START_YEAR;
        }
        
        log.debug("Using configured start year: {}", startYear);
        return startYear;
    }
    
    /**
     * 获取有效的结束年份
     *
     * @param context 生成器上下文
     * @param startYear 起始年份
     * @return 有效的结束年份
     */
    private Integer getValidEndYear(GeneratorContext context, Integer startYear) {
        Integer endYear = context.getConfig(CONFIG_END_YEAR, Integer.class);
        if (endYear == null) {
            log.debug("No end year configured, using default: {}", DEFAULT_END_YEAR);
            return DEFAULT_END_YEAR;
        }
        
        int currentYear = LocalDate.now().getYear();
        if (endYear < 1900 || endYear > currentYear) {
            log.warn("Invalid end year: {}, using default end year {}", endYear, DEFAULT_END_YEAR);
            return DEFAULT_END_YEAR;
        }
        
        if (endYear < startYear) {
            log.warn("End year {} is before start year {}, swapping values", endYear, startYear);
            return startYear;
        }
        
        log.debug("Using configured end year: {}", endYear);
        return endYear;
    }
    
    /**
     * 生成公历日期（格式：YYYYMMDD）。
     * <p>
     * 该方法根据指定的年份范围随机生成一个有效的公历日期：
     * 1. 在起始年份和结束年份之间随机选择一个年份
     * 2. 随机选择1-12月中的一个月份
     * 3. 根据年月确定该月的实际天数，然后随机选择一个有效的日期
     * </p>
     *
     * @param random 随机数生成器
     * @param startYear 起始年份
     * @param endYear 结束年份
     * @return 格式为YYYYMMDD的公历日期字符串
     */
    private String generateGregorianDate(Random random, int startYear, int endYear) {
        // 随机生成年份
        int year = startYear + random.nextInt(endYear - startYear + 1);
        
        // 随机生成月份（1-12月）
        int month = 1 + random.nextInt(12);
        
        // 根据年月确定该月的天数并随机生成日期
        int maxDay = YearMonth.of(year, month).lengthOfMonth();
        int day = 1 + random.nextInt(maxDay);
        
        // 格式化为YYYYMMDD
        return String.format("%04d%02d%02d", year, month, day);
    }
    
    /**
     * 生成农历日期（格式：YYYYMMDD）。
     * <p>
     * 该方法根据指定的年份范围随机生成一个农历日期：
     * 1. 在起始年份和结束年份之间随机选择一个年份
     * 2. 随机选择1-12月中的一个月份
     * 3. 随机决定是大月(30天)还是小月(29天)，然后随机选择一个有效的日期
     * </p>
     * <p>
     * 注意：这是简化实现，没有考虑闰月和实际农历历法的复杂规则。
     * 在实际应用中，可能需要更复杂的农历转换算法。
     * </p>
     *
     * @param random 随机数生成器
     * @param startYear 起始年份
     * @param endYear 结束年份
     * @return 格式为YYYYMMDD的农历日期字符串
     */
    private String generateLunarDate(Random random, int startYear, int endYear) {
        // 随机生成年份
        int year = startYear + random.nextInt(endYear - startYear + 1);
        
        // 随机生成月份（1-12月）
        int month = 1 + random.nextInt(12);
        
        // 随机决定是大月还是小月（大月30天，小月29天）并随机生成日期
        int maxDay = LUNAR_MONTH_DAYS[random.nextInt(LUNAR_MONTH_DAYS.length)];
        int day = 1 + random.nextInt(maxDay);
        
        // 格式化为YYYYMMDD
        return String.format("%04d%02d%02d", year, month, day);
    }

    /**
     * 计算身份证校验码（符合GB11643-1999标准）。
     * <p>
     * 校验码计算规则：
     * 1. 对前17位数字本体码加权求和，权重分别为：7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2
     * 2. 将和数除以11，得到余数
     * 3. 根据余数查表获得校验码，对应关系为：
     *    余数：0 1 2 3 4 5 6 7 8 9 10
     *    校验码：1 0 X 9 8 7 6 5 4 3 2
     * </p>
     *
     * @param base 身份证前17位数字字符串
     * @return 校验码字符（数字0-9或字符X）
     */
    private char calculateChecksum(String base) {
        // 身份证校验码计算权重和校验码映射
        int[] weight = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] validate = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        
        // 计算加权和
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (base.charAt(i) - '0') * weight[i];
        }
        
        // 返回校验码
        return validate[sum % 11];
    }

    /**
     * 配置参数名称常量
     */
    private static final String CONFIG_START_YEAR = "startYear";
    private static final String CONFIG_END_YEAR = "endYear";
    private static final String CONFIG_USE_LUNAR = "useLunar";
    private static final String CONFIG_GENDER = "gender";
    
    /**
     * 性别常量
     */
    private static final String GENDER_MALE = "male";
    private static final String GENDER_FEMALE = "female";
    
    /**
     * 支持的类型标识常量
     */
    private static final String[] SUPPORTED_TYPES = {"idcard", "id_card", "citizen_id"};
    
    /**
     * 判断生成器是否支持指定类型。
     * <p>
     * 该方法用于检查当前生成器是否支持指定的数据类型标识。
     * 身份证生成器支持以下类型标识（不区分大小写）：
     * - "idcard"
     * - "ID_CARD"
     * - "citizen_id"
     * </p>
     *
     * @param type 数据类型标识字符串
     * @return 如果支持该类型则返回true，否则返回false
     */
    @Override
    public boolean supports(String type) {
        if (type == null) {
            return false;
        }
        
        String lowerType = type.toLowerCase();
        for (String supportedType : SUPPORTED_TYPES) {
            if (supportedType.equals(lowerType)) {
                return true;
            }
        }
        return false;
    }
}
