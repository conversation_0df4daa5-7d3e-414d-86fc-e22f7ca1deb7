package com.dataforge.generators;

import com.dataforge.core.GeneratorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 身份证生成器单元测试类
 *
 * 测试中国居民身份证号码生成器的各项功能，包括：
 * 1. 支持的数据类型
 * 2. 生成的身份证号码格式
 * 3. 校验码计算
 * 4. 地区码、出生日期、顺序码的有效性
 * 5. 自定义配置参数
 * 6. 农历日期生成
 * 7. 性别控制功能
 */
class IdCardGeneratorTest {

    private IdCardGenerator idCardGenerator;
    private GeneratorContext mockContext;

    @BeforeEach
    void setUp() {
        idCardGenerator = new IdCardGenerator();
        mockContext = mock(GeneratorContext.class);
        when(mockContext.getSeed()).thenReturn(12345L);
    }

    @Test
    void testSupports() {
        assertTrue(idCardGenerator.supports("idcard"));
        assertTrue(idCardGenerator.supports("ID_CARD"));
        assertTrue(idCardGenerator.supports("citizen_id"));
        assertFalse(idCardGenerator.supports("invalid_type"));
        assertFalse(idCardGenerator.supports(null));
    }

    /**
     * 测试生成的身份证号码格式是否正确
     */
    @Test
    void testIdCardFormat() {
        String idCard = idCardGenerator.generate(mockContext);
        
        assertNotNull(idCard);
        assertEquals(18, idCard.length());
        assertTrue(idCard.matches("^\\d{17}[0-9X]$"));
    }

    /**
     * 测试校验码计算是否正确
     */
    @Test
    void testCheckDigitCalculation() {
        String idCard = idCardGenerator.generate(mockContext);
        char lastChar = idCard.charAt(17);
        
        // 验证校验码计算是否正确
        int sum = 0;
        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] checkDigits = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        
        for (int i = 0; i < 17; i++) {
            sum += (idCard.charAt(i) - '0') * weights[i];
        }
        int remainder = sum % 11;
        char expectedCheckDigit = checkDigits[remainder];
        
        assertEquals(expectedCheckDigit, lastChar);
    }

    /**
     * 测试地区码是否有效
     */
    @Test
    void testRegionCodeValid() {
        String idCard = idCardGenerator.generate(mockContext);
        String regionCode = idCard.substring(0, 6);
        
        // 验证地区码是否在有效范围内
        assertTrue(regionCode.matches("^[1-9]\\d{5}$"));
    }

    /**
     * 测试出生日期是否有效
     */
    @Test
    void testBirthDateValid() {
        String idCard = idCardGenerator.generate(mockContext);
        String birthDate = idCard.substring(6, 14);
        
        // 验证出生日期格式
        assertTrue(birthDate.matches("^(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])$"));
    }

    /**
     * 测试顺序码是否有效
     */
    @Test
    void testSequenceNumberValid() {
        String idCard = idCardGenerator.generate(mockContext);
        String sequenceNumber = idCard.substring(14, 17);
        
        // 验证顺序码
        assertTrue(sequenceNumber.matches("^\\d{3}$"));
    }
    
    /**
     * 测试不同配置参数下的身份证生成
     */
    @Test
    void testWithCustomConfig() {
        // 配置自定义年份范围
        when(mockContext.getConfig("startYear", Integer.class)).thenReturn(1990);
        when(mockContext.getConfig("endYear", Integer.class)).thenReturn(2000);
        
        String idCard = idCardGenerator.generate(mockContext);
        String birthYear = idCard.substring(6, 10);
        
        // 验证出生年份在配置的范围内
        int year = Integer.parseInt(birthYear);
        assertTrue(year >= 1990 && year <= 2000);
    }
    
    /**
     * 测试农历日期生成
     */
    @Test
    void testLunarDateGeneration() {
        // 配置使用农历日期
        when(mockContext.getConfig("useLunar", Boolean.class)).thenReturn(true);
        
        String idCard = idCardGenerator.generate(mockContext);
        String birthDate = idCard.substring(6, 14);
        
        // 验证出生日期格式
        assertTrue(birthDate.matches("^(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])$"));
    }
    
    /**
     * 测试不同随机种子生成的身份证号码是否不同
     */
    @Test
    void testDifferentSeedGeneratesDifferentIds() {
        // 测试第一个种子
        when(mockContext.getSeed()).thenReturn(12345L);
        String idCard1 = idCardGenerator.generate(mockContext);
        
        // 测试第二个种子
        when(mockContext.getSeed()).thenReturn(67890L);
        String idCard2 = idCardGenerator.generate(mockContext);
        
        // 测试第三个种子
        when(mockContext.getSeed()).thenReturn(54321L);
        String idCard3 = idCardGenerator.generate(mockContext);
        
        // 验证所有生成的身份证号码格式正确
        assertNotNull(idCard1);
        assertNotNull(idCard2);
        assertNotNull(idCard3);
        assertEquals(18, idCard1.length());
        assertEquals(18, idCard2.length());
        assertEquals(18, idCard3.length());
        
        // 验证不同种子生成的身份证号码不同
        assertNotEquals(idCard1, idCard2);
        assertNotEquals(idCard1, idCard3);
        assertNotEquals(idCard2, idCard3);
    }
    
    /**
     * 测试无效配置参数的处理
     */
    @Test
    void testInvalidConfigHandling() {
        // 配置无效的年份范围
        when(mockContext.getConfig("startYear", Integer.class)).thenReturn(1800); // 小于1900
        when(mockContext.getConfig("endYear", Integer.class)).thenReturn(2100); // 大于当前年份
        
        String idCard = idCardGenerator.generate(mockContext);
        
        // 验证身份证格式正确，说明生成器能够处理无效配置
        assertNotNull(idCard);
        assertEquals(18, idCard.length());
        assertTrue(idCard.matches("^\\d{17}[0-9X]$"));
    }
    
    /**
     * 测试性别控制功能 - 男性
     */
    @Test
    void testGenderControlMale() {
        // 配置性别为男性
        when(mockContext.getConfig("gender", String.class)).thenReturn("male");
        
        String idCard = idCardGenerator.generate(mockContext);
        
        // 验证身份证格式正确
        assertNotNull(idCard);
        assertEquals(18, idCard.length());
        
        // 验证顺序码（第17位）是奇数，表示男性
        int sequenceLastDigit = Character.getNumericValue(idCard.charAt(16));
        assertEquals(1, sequenceLastDigit % 2);
    }
    
    /**
     * 测试性别控制功能 - 女性
     */
    @Test
    void testGenderControlFemale() {
        // 配置性别为女性
        when(mockContext.getConfig("gender", String.class)).thenReturn("female");
        
        String idCard = idCardGenerator.generate(mockContext);
        
        // 验证身份证格式正确
        assertNotNull(idCard);
        assertEquals(18, idCard.length());
        
        // 验证顺序码（第17位）是偶数，表示女性
        int sequenceLastDigit = Character.getNumericValue(idCard.charAt(16));
        assertEquals(0, sequenceLastDigit % 2);
    }
    
    /**
     * 测试无效性别配置的处理
     */
    @Test
    void testInvalidGenderConfig() {
        // 配置无效的性别
        when(mockContext.getConfig("gender", String.class)).thenReturn("invalid_gender");
        
        String idCard = idCardGenerator.generate(mockContext);
        
        // 验证身份证格式正确，说明生成器能够处理无效配置
        assertNotNull(idCard);
        assertEquals(18, idCard.length());
        assertTrue(idCard.matches("^\\d{17}[0-9X]$"));
    }
    
    /**
     * 测试空上下文处理
     */
    @Test
    void testNullContextHandling() {
        // 验证传入null上下文时抛出异常
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            idCardGenerator.generate(null);
        });
        
        // 验证异常消息
        assertEquals("Context cannot be null", exception.getMessage());
    }
}
