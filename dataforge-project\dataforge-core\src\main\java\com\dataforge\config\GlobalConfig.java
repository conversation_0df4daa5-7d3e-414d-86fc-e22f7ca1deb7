package com.dataforge.config;

import java.util.HashMap;
import java.util.Map;

public class GlobalConfig {
    public enum OutputFormat {
        CSV,
        JSON,
        SQL
    }
    
    private String outputFormat;
    private String outputPath;
    private int batchSize = 1000;
    private String sqlTableName = "test_data";
    private Map<String, Object> generatorConfigs = new HashMap<>();

    // Getters and setters
    public String getOutputFormat() {
        return outputFormat;
    }

    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat;
    }

    public String getOutputPath() {
        return outputPath;
    }

    public void setOutputPath(String outputPath) {
        this.outputPath = outputPath;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public Map<String, Object> getGeneratorConfigs() {
        return generatorConfigs;
    }

    public void setGeneratorConfigs(Map<String, Object> generatorConfigs) {
        this.generatorConfigs = generatorConfigs;
    }

    public String getSqlTableName() {
        return sqlTableName;
    }

    public void setSqlTableName(String sqlTableName) {
        this.sqlTableName = sqlTableName;
    }
}
