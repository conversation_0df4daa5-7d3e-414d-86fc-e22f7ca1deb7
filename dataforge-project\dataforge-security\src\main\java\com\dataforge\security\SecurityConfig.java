package com.dataforge.security;

import org.codehaus.groovy.control.customizers.SecureASTCustomizer;
import org.codehaus.groovy.control.CompilerConfiguration;
import java.util.Arrays;
import java.util.List;

/**
 * 安全加固配置中心
 * 统一管理所有安全相关配置
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
public class SecurityConfig {
    
    private static final SecurityConfig INSTANCE = new SecurityConfig();
    
    // 默认安全配置
    private final SecurityPolicy defaultPolicy;
    
    private SecurityConfig() {
        this.defaultPolicy = SecurityPolicy.builder()
            .maxScriptExecutionTime(100) // 100ms
            .maxMemoryUsage(10 * 1024 * 1024) // 10MB
            .enableInputValidation(true)
            .enableSensitiveDataMasking(true)
            .enableSqlInjectionCheck(true)
            .enableXssProtection(true)
            .maxStringLength(10000)
            .maxCollectionSize(100000)
            .build();
    }
    
    public static SecurityConfig getInstance() {
        return INSTANCE;
    }
    
    /**
     * 创建Groovy安全编译配置
     */
    public CompilerConfiguration createGroovySecurityConfig() {
        CompilerConfiguration config = new CompilerConfiguration();
        SecureASTCustomizer secure = new SecureASTCustomizer();
        
        // 白名单类 - 只允许基础类型和常用工具类
        secure.setClosuresAllowed(true);
        secure.setMethodDefinitionAllowed(false);
        
        List<String> importsWhitelist = Arrays.asList(
            "java.lang.*",
            "java.util.*",
            "java.math.*",
            "java.time.*"
        );
        secure.setImportsWhitelist(importsWhitelist);
        
        // 黑名单类和方法
        secure.setReceiversBlackList(Arrays.asList(
            "java.lang.System",
            "java.lang.Runtime",
            "java.io.File",
            "java.net.Socket",
            "java.lang.reflect.*"
        ));
        
        config.addCompilationCustomizers(secure);
        return config;
    }
    
    public SecurityPolicy getDefaultPolicy() {
        return defaultPolicy;
    }
}
