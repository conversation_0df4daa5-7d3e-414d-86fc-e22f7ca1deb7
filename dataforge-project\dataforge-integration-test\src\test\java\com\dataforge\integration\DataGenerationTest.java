package com.dataforge.integration;

import com.dataforge.core.DataForgeService;
import com.dataforge.core.GeneratorContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class DataGenerationTest extends BaseIntegrationTest {

    @Autowired
    private DataForgeService dataForgeService;

    @Test
    void shouldGenerateIdCards() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("idCard")
                .count(10)
                .build();
        
        Object result = dataForgeService.generate(context);
        assertNotNull(result, "身份证生成结果不应为null");
    }

    @Test
    void shouldGeneratePhoneNumbers() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("phoneNumber")
                .count(100)
                .build();
        
        Object result = dataForgeService.generate(context);
        assertNotNull(result, "手机号生成结果不应为null");
        assertTrue(result instanceof Iterable<?>, "应返回可迭代结果");
    }

    @Test
    void shouldGenerateBankCards() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("bankCard")
                .count(5)
                .build();
        
        Object result = dataForgeService.generate(context);
        assertNotNull(result, "银行卡生成结果不应为null");
    }

    @Test
    void shouldGenerateValidAddresses() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("address")
                .count(50)
                .validate(true)
                .build();
        
        Iterable<?> results = (Iterable<?>) dataForgeService.generate(context);
        assertNotNull(results, "地址生成结果不应为null");
        
        results.forEach(address -> {
            String addr = address.toString();
            String[] parts = addr.split(",");
            
            // 验证省市区三级结构
            assertTrue(parts.length >= 3, "地址应包含省市区三级: " + addr);
            
            // 验证省份格式
            assertTrue(parts[0].matches("^[\\u4e00-\\u9fa5]+省$"), "省份格式不正确: " + parts[0]);
            
            // 验证城市格式
            assertTrue(parts[1].matches("^[\\u4e00-\\u9fa5]+市$"), "城市格式不正确: " + parts[1]);
            
            // 验证区县格式
            assertTrue(parts[2].matches("^[\\u4e00-\\u9fa5]+(区|县)$"), "区县格式不正确: " + parts[2]);
            
            // 验证邮编(如果存在)
            if (parts.length > 3 && parts[parts.length-1].matches("\\d{6}")) {
                String postcode = parts[parts.length-1];
                assertTrue(postcode.matches("^[1-9]\\d{5}$"), "邮编格式不正确: " + postcode);
            }
        });
    }

    @Test
    void shouldHandleInvalidAddressRequests() {
        // 测试无效数量
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("address")
                .count(-1)
                .build();
        });

        // 测试无效类型
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("invalidAddressType")
                .count(10)
                .build();
        });
    }

    @Test
    void shouldValidateIdCards() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("idCard")
                .count(10)
                .validate(true)
                .build();
        
        Iterable<?> results = (Iterable<?>) dataForgeService.generate(context);
        assertNotNull(results, "身份证校验结果不应为null");
        
        results.forEach(idCard -> {
            String idStr = idCard.toString();
            assertEquals(18, idStr.length(), "身份证号长度应为18位");
            assertTrue(idStr.matches("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$"), 
                "身份证格式不正确: " + idStr);
            
            // 校验位验证
            char[] idChars = idStr.toCharArray();
            int[] weight = {7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2};
            char[] verifyCodes = {'1','0','X','9','8','7','6','5','4','3','2'};
            
            int sum = 0;
            for(int i=0; i<17; i++) {
                sum += (idChars[i]-'0') * weight[i];
            }
            char verifyCode = verifyCodes[sum % 11];
            assertEquals(verifyCode, Character.toUpperCase(idChars[17]), 
                "身份证校验位不正确: " + idStr);
        });
    }

    @Test
    void shouldHandleInvalidIdCardRequests() {
        // 测试无效数量
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("idCard")
                .count(-1)
                .build();
        });

        // 测试无效类型
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("invalidType")
                .count(10)
                .build();
        });
    }

    @Test
    void shouldValidateBankCards() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("bankCard")
                .count(10)
                .validate(true)
                .build();
        
        Iterable<?> results = (Iterable<?>) dataForgeService.generate(context);
        assertNotNull(results, "银行卡校验结果不应为null");
        
        results.forEach(bankCard -> {
            String cardNumber = bankCard.toString();
            assertTrue(cardNumber.matches("^[1-9]\\d{15,18}$"), "银行卡号格式不正确: " + cardNumber);
            
            // Luhn算法验证
            char[] digits = cardNumber.toCharArray();
            int sum = 0;
            boolean alternate = false;
            for (int i = digits.length - 1; i >= 0; i--) {
                int n = digits[i] - '0';
                if (alternate) {
                    n *= 2;
                    if (n > 9) {
                        n = (n % 10) + 1;
                    }
                }
                sum += n;
                alternate = !alternate;
            }
            assertTrue(sum % 10 == 0, "银行卡号校验失败(Luhn算法): " + cardNumber);
        });
    }

    @Test
    void shouldHandleInvalidBankCardRequests() {
        // 测试无效数量
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("bankCard")
                .count(-1)
                .build();
        });

        // 测试无效卡类型
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("invalidCardType")
                .count(10)
                .build();
        });
    }

    @Test
    @Timeout(5) // 5秒超时
    void shouldGenerate100kIdCardsUnder5Seconds() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("idCard")
                .count(100_000)
                .build();
        
        Object result = dataForgeService.generate(context);
        assertNotNull(result, "批量身份证生成结果不应为null");
    }

    @Test
    @Timeout(10) // 10秒超时
    void shouldGenerate1MPhoneNumbersUnder10Seconds() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("phoneNumber")
                .count(1_000_000)
                .build();
        
        Object result = dataForgeService.generate(context);
        assertNotNull(result, "批量手机号生成结果不应为null");
    }

    @Test
    void shouldGenerateValidCompanyInfo() {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("company")
                .count(20)
                .validate(true)
                .build();
        
        Iterable<?> results = (Iterable<?>) dataForgeService.generate(context);
        assertNotNull(results, "企业信息生成结果不应为null");
        
        results.forEach(company -> {
            String[] parts = company.toString().split(",");
            
            // 验证企业名称格式
            assertTrue(parts[0].matches("^[\\u4e00-\\u9fa5（）()]{4,50}$"), "企业名称格式不正确: " + parts[0]);
            
            // 验证统一社会信用代码(18位)
            if (parts.length > 1) {
                String creditCode = parts[1];
                assertEquals(18, creditCode.length(), "统一社会信用代码长度应为18位");
                assertTrue(creditCode.matches("^[0-9A-Z]{18}$"), "统一社会信用代码格式不正确: " + creditCode);
                
                // 验证校验位
                char[] chars = creditCode.toCharArray();
                int[] weights = {1,3,9,27,19,26,16,17,20,29,25,13,8,24,10,30,28};
                String checkCodes = "0123456789ABCDEFGHJKLMNPQRTUWXY";
                int sum = 0;
                for (int i = 0; i < 17; i++) {
                    char c = chars[i];
                    int code = (c >= '0' && c <= '9') ? (c - '0') : (c - 'A' + 10);
                    sum += code * weights[i];
                }
                int checkCodeIndex = 31 - (sum % 31);
                char expectedCheckCode = checkCodes.charAt(checkCodeIndex);
                assertEquals(expectedCheckCode, chars[17], "统一社会信用代码校验位不正确: " + creditCode);
            }
            
            // 验证组织机构代码(9位)
            if (parts.length > 2) {
                String orgCode = parts[2];
                assertEquals(9, orgCode.length(), "组织机构代码长度应为9位");
                assertTrue(orgCode.matches("^[0-9A-Z]{8}-?[0-9A-Z]$"), "组织机构代码格式不正确: " + orgCode);
            }
        });
    }

    @Test
    void shouldHandleInvalidCompanyRequests() {
        // 测试无效数量
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("company")
                .count(-1)
                .build();
        });

        // 测试无效类型
        assertThrows(IllegalArgumentException.class, () -> {
            new GeneratorContext.Builder()
                .type("invalidCompanyType")
                .count(10)
                .build();
        });
    }

    @Test
    void shouldGenerateDataWithDifferentBatchSizes() {
        int[] batchSizes = {100, 1_000, 10_000, 100_000};
        
        for (int size : batchSizes) {
            GeneratorContext context = new GeneratorContext.Builder()
                    .type("idCard")
                    .count(size)
                    .build();
            
            long startTime = System.currentTimeMillis();
            Object result = dataForgeService.generate(context);
            long duration = System.currentTimeMillis() - startTime;
            
            assertNotNull(result, "批量生成结果不应为null");
            System.out.printf("生成 %d 条身份证数据耗时: %d ms%n", size, duration);
        }
    }
}

// JMH基准测试类
@State(Scope.Benchmark)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@BenchmarkMode(Mode.AverageTime)
class DataGenerationBenchmark {
    @Autowired
    private DataForgeService dataForgeService;

    @Benchmark
    public void benchmarkIdCardGeneration(Blackhole bh) {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("idCard")
                .count(10_000)
                .build();
        
        bh.consume(dataForgeService.generate(context));
    }

    @Benchmark
    public void benchmarkPhoneNumberGeneration(Blackhole bh) {
        GeneratorContext context = new GeneratorContext.Builder()
                .type("phoneNumber")
                .count(10_000)
                .build();
        
        bh.consume(dataForgeService.generate(context));
    }
}
