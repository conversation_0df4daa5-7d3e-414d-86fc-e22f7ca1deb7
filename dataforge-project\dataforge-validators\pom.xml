<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dataforge</groupId>
        <artifactId>dataforge-project</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <artifactId>dataforge-validators</artifactId>
    <name>DataForge Validators</name>
    
    <dependencies>
        <dependency>
            <groupId>com.dataforge</groupId>
            <artifactId>dataforge-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 添加Groovy依赖 -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.14</version>
        </dependency>
        <!-- 添加安全模块依赖 -->
        <dependency>
            <groupId>com.dataforge</groupId>
            <artifactId>dataforge-security</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
