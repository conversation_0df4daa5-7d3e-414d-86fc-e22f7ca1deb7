package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;
import org.apache.commons.lang3.RandomStringUtils;
import java.util.Random;

/**
 * 企业信息生成器，包含统一社会信用代码和企业名称生成
 */
public class EnterpriseInfoGenerator implements DataGenerator<String> {
    private static final String[] REGION_CODES = {"11", "12", "13", "31", "32"};
    private static final String[] ORG_CATEGORIES = {"1", "2", "3", "9"};
    private static final String[] INDUSTRY_KEYWORDS = {"科技", "信息", "网络", "电子", "商贸", 
        "实业", "咨询", "服务", "建筑", "物流"};
    private static final String[] COMPANY_SUFFIXES = {"有限公司", "有限责任公司", "集团", "股份有限公司"};

    @Override
    public String generate(GeneratorContext context) {
        Random random = new Random(context.getSeed());
        
        // 生成企业名称
        String companyName = generateCompanyName(random);
        
        // 生成统一社会信用代码
        String creditCode = generateCreditCode(random);
        
        // 从统一社会信用代码中提取组织机构代码(第9-17位)
        String orgCode = creditCode.substring(8, 17);
        
        // 生成LEI码(20位字母数字)
        String leiCode = generateLeiCode(random);
        
        // 返回JSON格式
        return String.format(
            "{\"name\":\"%s\",\"unifiedSocialCreditCode\":\"%s\",\"organizationCode\":\"%s\",\"leiCode\":\"%s\"}",
            companyName, creditCode, orgCode, leiCode
        );
    }

    private String generateLeiCode(Random random) {
        // LEI码格式: 20位大写字母和数字
        return RandomStringUtils.random(20, 0, 0, true, true, null, random)
            .toUpperCase();
    }

    private String generateCompanyName(Random random) {
        // 1. 行业关键词
        String industry = INDUSTRY_KEYWORDS[random.nextInt(INDUSTRY_KEYWORDS.length)];
        
        // 2. 随机字号(3-6个汉字)
        String name = RandomStringUtils.random(3 + random.nextInt(4), 0x4e00, 0x9fa5, false, false);
        
        // 3. 公司后缀
        String suffix = COMPANY_SUFFIXES[random.nextInt(COMPANY_SUFFIXES.length)];
        
        return industry + name + suffix;
    }

    private String generateCreditCode(Random random) {
        // 1. 登记管理部门代码(1位)
        String deptCode = "9"; // 工商
        
        // 2. 机构类别代码(1位)
        String orgCategory = ORG_CATEGORIES[random.nextInt(ORG_CATEGORIES.length)];
        
        // 3. 登记管理机关行政区划码(6位)
        String regionCode = REGION_CODES[random.nextInt(REGION_CODES.length)] 
            + String.format("%04d", random.nextInt(9999));
            
        // 4. 组织机构代码(9位)
        String orgCode = RandomStringUtils.randomNumeric(8);
        orgCode += calculateOrgCodeCheckDigit(orgCode);
        
        // 5. 校验码(1位)
        String creditCode = deptCode + orgCategory + regionCode + orgCode;
        creditCode += calculateCreditCodeCheckDigit(creditCode);
        
        return creditCode;
    }

    private char calculateOrgCodeCheckDigit(String code) {
        int[] weights = {3, 7, 9, 10, 5, 8, 4, 2};
        int sum = 0;
        for (int i = 0; i < 8; i++) {
            sum += (code.charAt(i) - '0') * weights[i];
        }
        int mod = 11 - sum % 11;
        return mod == 10 ? 'X' : (char)(mod + '0');
    }

    private char calculateCreditCodeCheckDigit(String code) {
        int[] weights = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char c = code.charAt(i);
            int value = Character.isDigit(c) ? (c - '0') : (c - 'A' + 10);
            sum += value * weights[i];
        }
        int mod = 31 - sum % 31;
        return mod == 31 ? '0' : (mod < 10 ? (char)(mod + '0') : (char)(mod - 10 + 'A'));
    }

    @Override
    public boolean supports(String type) {
        return "enterprise".equalsIgnoreCase(type) 
            || "ENTERPRISE_INFO".equalsIgnoreCase(type)
            || "creditCode".equalsIgnoreCase(type)
            || "companyName".equalsIgnoreCase(type);
    }
}
