<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.dataforge</groupId>
    <artifactId>dataforge-project</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>DataForge Parent</name>
    
    <modules>
        <module>dataforge-core</module>
        <module>dataforge-generators</module>
        <module>dataforge-validators</module>
        <module>dataforge-security</module>
        <module>dataforge-cli</module>
        <module>dataforge-api</module>
    </modules>
    
    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        
        <!-- 统一依赖版本 -->
        <lombok.version>1.18.30</lombok.version>
        <slf4j.version>2.0.9</slf4j.version>
        <groovy.version>4.0.18</groovy.version>
        <junit.version>5.9.2</junit.version>
        <mockito.version>5.3.1</mockito.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <javafaker.version>1.0.2</javafaker.version>
        <!-- Spring Boot版本 -->
        <spring-boot.version>3.2.0</spring-boot.version>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <!-- Groovy依赖 -->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.dataforge</groupId>
                <artifactId>dataforge-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dataforge</groupId>
                <artifactId>dataforge-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <!-- 测试依赖 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            
            <!-- 添加公共依赖管理 -->
            <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>${javafaker.version}</version>
            </dependency>
            <!-- Mockito测试依赖 -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
