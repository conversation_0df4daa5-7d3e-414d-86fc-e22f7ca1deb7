package com.dataforge.security.sandbox;

import com.dataforge.security.SecurityConfig;
import com.dataforge.security.SecurityPolicy;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Groovy脚本安全沙箱
 * 提供安全的Groovy脚本执行环境
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
public class GroovySandbox {
    
    private static final Logger logger = LoggerFactory.getLogger(GroovySandbox.class);
    
    private final SecurityPolicy policy;
    private final CompilerConfiguration compilerConfig;
    private final ExecutorService executor;
    private final ConcurrentHashMap<String, Script> scriptCache;
    private final SandboxClassLoader classLoader;
    
    public GroovySandbox() {
        this.policy = SecurityConfig.getInstance().getDefaultPolicy();
        this.compilerConfig = SecurityConfig.getInstance().createGroovySecurityConfig();
        this.executor = Executors.newCachedThreadPool(new SecurityThreadFactory());
        this.scriptCache = new ConcurrentHashMap<>();
        this.classLoader = new SandboxClassLoader(getClass().getClassLoader());
    }
    
    /**
     * 安全执行Groovy脚本
     */
    /**
     * 编译Groovy脚本
     * @param scriptContent 脚本内容
     * @return 编译后的Script对象
     */
    public Script compileScript(String scriptContent) {
        GroovyShell shell = new GroovyShell(classLoader, compilerConfig);
        return shell.parse(scriptContent);
    }

    public Object executeScript(String scriptContent, Object input) throws SecurityException {
        // 输入验证
        if (scriptContent == null || scriptContent.trim().isEmpty()) {
            throw new SecurityException("脚本内容不能为空");
        }
        
        // 脚本长度检查
        if (scriptContent.length() > 10000) {
            throw new SecurityException("脚本内容过长");
        }
        
        try {
            // 使用缓存的脚本或创建新脚本
            Script script = scriptCache.computeIfAbsent(scriptContent, k -> compileScript(k));
            
            // 设置脚本变量
            script.setProperty("input", input);
            
            // 在受限环境中执行
            return executeWithTimeout(script);
            
        } catch (Exception e) {
            logger.error("脚本执行失败: {}", e.getMessage());
            throw new SecurityException("脚本执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 带超时控制的脚本执行
     */
    private Object executeWithTimeout(Script script) throws Exception {
        Future<Object> future = executor.submit(() -> {
            Thread currentThread = Thread.currentThread();
            MemoryMonitor monitor = new MemoryMonitor(currentThread, policy.getMaxMemoryUsage());
            Thread monitorThread = new Thread(monitor, "memory-monitor");
            monitorThread.setDaemon(true);
            monitorThread.start();
            
            try {
                return script.run();
            } catch (OutOfMemoryError e) {
                throw new SecurityException("脚本内存使用超出限制");
            } finally {
                monitor.stop();
                monitorThread.interrupt();
            }
        });
        
        try {
            return future.get(policy.getMaxScriptExecutionTime(), TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            throw new SecurityException("脚本执行超时");
        } catch (ExecutionException e) {
            if (e.getCause() instanceof SecurityException) {
                throw (SecurityException) e.getCause();
            }
            throw new SecurityException("脚本执行异常: " + e.getCause().getMessage());
        }
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        scriptCache.clear();
    }
    
    /**
     * 关闭沙箱
     */
    public void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 安全线程工厂
     */
    private static class SecurityThreadFactory implements ThreadFactory {
        private final AtomicLong counter = new AtomicLong(0);
        
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, "groovy-sandbox-" + counter.incrementAndGet());
            thread.setDaemon(true);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }
    
    /**
     * 安全类加载器
     */
    private static class SandboxClassLoader extends ClassLoader {
        private static final List<String> WHITELIST = Arrays.asList(
            "java.lang.",
            "java.util.",
            "java.math.",
            "java.time.",
            "groovy.lang."
        );
        
        public SandboxClassLoader(ClassLoader parent) {
            super(parent);
        }
        
        @Override
        protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
            // 检查白名单
            if (!isAllowed(name)) {
                throw new SecurityException("禁止加载类: " + name);
            }
            return super.loadClass(name, resolve);
        }
        
        private boolean isAllowed(String className) {
            for (String prefix : WHITELIST) {
                if (className.startsWith(prefix)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 动态内存监控线程
     */
    private class MemoryMonitor implements Runnable {
        private final long maxBytes;
        private final Thread targetThread;
        private volatile boolean running = true;

        public MemoryMonitor(Thread targetThread, long maxBytes) {
            this.targetThread = targetThread;
            this.maxBytes = maxBytes;
        }

        public void stop() {
            running = false;
        }

        @Override
        public void run() {
            while (running) {
                long usedMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
                if (usedMemory > maxBytes) {
                    targetThread.interrupt();
                    throw new SecurityException("内存使用超出限制: " + usedMemory + " > " + maxBytes);
                }
                try {
                    Thread.sleep(50); // 每50ms检查一次
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
