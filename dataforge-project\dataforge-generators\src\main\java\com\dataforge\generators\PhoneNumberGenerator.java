package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;
import java.util.Random;

/**
 * 手机号生成器，支持中国三大运营商号段
 * 遵循GB/T 3976.1-2023《电信网编号计划》规范
 */
public class PhoneNumberGenerator implements DataGenerator<String> {

    // 运营商号段定义（移动/联通/电信/虚拟运营商）
    private static final String[] MOBILE_PREFIXES = {"134", "135", "136", "137", "138", "139", "150", "151", "152", "157", "158", "159", "178", "182", "183", "184", "187", "188", "198"};
    private static final String[] UNICOM_PREFIXES = {"130", "131", "132", "155", "156", "166", "175", "176", "185", "186"};
    private static final String[] TELECOM_PREFIXES = {"133", "153", "177", "180", "181", "189", "199"};
    private static final String[] VIRTUAL_PREFIXES = {"170", "171"};

    @Override
    public String generate(GeneratorContext context) {
        Random random = new Random(context.getSeed());
        String prefix = selectRandomPrefix(random);
        return prefix + generateSuffix(random);
    }

    private String selectRandomPrefix(Random random) {
        // 组合所有号段
        String[] allPrefixes = concatArrays(MOBILE_PREFIXES, UNICOM_PREFIXES, TELECOM_PREFIXES, VIRTUAL_PREFIXES);
        return allPrefixes[random.nextInt(allPrefixes.length)];
    }

    private String generateSuffix(Random random) {
        StringBuilder suffix = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            suffix.append(random.nextInt(10));
        }
        return suffix.toString();
    }

    @SafeVarargs
    private final String[] concatArrays(String[]... arrays) {
        int length = 0;
        for (String[] array : arrays) {
            length += array.length;
        }
        String[] result = new String[length];
        int index = 0;
        for (String[] array : arrays) {
            System.arraycopy(array, 0, result, index, array.length);
            index += array.length;
        }
        return result;
    }

    @Override
    public boolean supports(String type) {
        return "phonenumber".equalsIgnoreCase(type);
    }
}
