package com.dataforge.core;

import com.dataforge.config.GlobalConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveTask;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

public class DataForgeService {
    private final GeneratorFactory generatorFactory;
    private final GlobalConfig config;
    private final ForkJoinPool forkJoinPool;
    
    public DataForgeService(GlobalConfig config) {
        this.config = config;
        this.generatorFactory = new GeneratorFactory();
        this.forkJoinPool = new ForkJoinPool(Runtime.getRuntime().availableProcessors() * 2);
    }
    
    @SuppressWarnings("unchecked")
    public <T> List<T> generateBatch(String type) {
        DataGenerator<T> generator = (DataGenerator<T>) generatorFactory.getGenerator(type);
        
        return forkJoinPool.submit(() -> 
            LongStream.range(0, config.getBatchSize())
                .parallel()
                .mapToObj(i -> {
                    Map<String, Object> generatorConfig = (Map<String, Object>) 
                        config.getGeneratorConfigs().getOrDefault(type, new HashMap<>());
                    GeneratorContext context = new GeneratorContext(
                        generatorConfig,
                        System.currentTimeMillis() + i
                    );
                    return generator.generate(context);
                })
                .collect(Collectors.toList())
        ).join();
    }
    
    public void outputResults(List<Object> results) {
        switch (GlobalConfig.OutputFormat.valueOf(config.getOutputFormat())) {
            case CSV:
                outputAsCsv(results);
                break;
            case JSON:
                outputAsJson(results);
                break;
            case SQL:
                outputAsSql(results);
                break;
            default:
                throw new IllegalArgumentException("Unsupported output format: " + config.getOutputFormat());
        }
    }

    private void outputAsCsv(List<Object> results) {
        if (results.isEmpty()) return;
        
        // 输出CSV头
        if (results.get(0) instanceof Map) {
            Map<?, ?> first = (Map<?, ?>) results.get(0);
            System.out.println(String.join(",", first.keySet().stream()
                .map(Object::toString)
                .collect(Collectors.toList())));
        }

        // 输出数据行
        results.forEach(item -> {
            if (item instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) item;
                System.out.println(String.join(",", map.values().stream()
                    .map(v -> "\"" + v.toString().replace("\"", "\"\"") + "\"")
                    .collect(Collectors.toList())));
            } else {
                System.out.println("\"" + item.toString().replace("\"", "\"\"") + "\"");
            }
        });
    }

    private void outputAsJson(List<Object> results) {
        StringBuilder json = new StringBuilder("[");
        results.forEach(item -> {
            if (json.length() > 1) json.append(",");
            if (item instanceof Map) {
                json.append(mapToJson((Map<?, ?>) item));
            } else {
                json.append("\"").append(item.toString().replace("\"", "\\\"")).append("\"");
            }
        });
        json.append("]");
        System.out.println(json.toString());
    }

    private String mapToJson(Map<?, ?> map) {
        StringBuilder json = new StringBuilder("{");
        map.forEach((k, v) -> {
            if (json.length() > 1) json.append(",");
            json.append("\"").append(k).append("\":");
            if (v instanceof Map) {
                json.append(mapToJson((Map<?, ?>) v));
            } else {
                json.append("\"").append(v.toString().replace("\"", "\\\"")).append("\"");
            }
        });
        return json.append("}").toString();
    }

    private void outputAsSql(List<Object> results) {
        if (results.isEmpty()) return;
        
        String tableName = config.getSqlTableName();
        if (results.get(0) instanceof Map) {
            Map<?, ?> first = (Map<?, ?>) results.get(0);
            String columns = first.keySet().stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
            
            results.forEach(item -> {
                Map<?, ?> map = (Map<?, ?>) item;
                String values = map.values().stream()
                    .map(v -> "'" + v.toString().replace("'", "''") + "'")
                    .collect(Collectors.joining(","));
                System.out.println("INSERT INTO " + tableName + " (" + columns + ") VALUES (" + values + ");");
            });
        } else {
            results.forEach(item -> {
                System.out.println("INSERT INTO " + tableName + " VALUES ('" + 
                    item.toString().replace("'", "''") + "');");
            });
        }
    }
}
