package com.dataforge.validators;

import com.dataforge.core.Validator;
import com.dataforge.core.ValidationResult;
import com.dataforge.security.sandbox.GroovySandbox;
import com.dataforge.security.SecurityPolicy;
import groovy.lang.Binding;
import groovy.lang.Script;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于Groovy脚本的动态校验器实现
 *
 * <p>该类实现了Validator接口，允许通过Groovy脚本定义灵活的数据校验规则。
 * 脚本执行在安全沙箱环境中，防止恶意代码执行。</p>
 *
 * <p>使用示例：
 * <pre>
 * SecurityPolicy policy = new SecurityPolicy();
 * GroovyValidator validator = new GroovyValidator("age_check", "value > 18", policy);
 * ValidationResult result = validator.validate(20); // 返回valid
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @version 1.1
 */
public class GroovyValidator implements Validator {

    /** 校验规则唯一标识 */
    private final String ruleId;
    
    /** Groovy脚本内容 */
    private final String scriptContent;
    
    /** Groovy安全沙箱环境 */
    private final GroovySandbox sandbox;
    
    /** 预编译脚本缓存 */
    private final Map<String, Script> scriptCache = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param ruleId 校验规则ID
     * @param scriptContent Groovy脚本内容
     * @param securityPolicy 安全策略配置
     */
    public GroovyValidator(String ruleId, String scriptContent, SecurityPolicy securityPolicy) {
        this.ruleId = ruleId;
        this.scriptContent = scriptContent;
        this.sandbox = new GroovySandbox(securityPolicy);
    }

    /**
     * 执行数据校验
     *
     * @param value 待校验的数据
     * @return 校验结果对象
     */
    @Override
    public ValidationResult validate(Object value) {
        try {
            Script script = getOrCompileScript();
            Binding binding = new Binding();
            binding.setVariable("value", value);
            script.setBinding(binding);
            
            Object result = script.run();
            if (result instanceof Boolean) {
                return (Boolean) result ?
                    ValidationResult.valid() :
                    ValidationResult.invalid("校验规则 '" + ruleId + "' 验证失败");
            } else {
                return ValidationResult.invalid("脚本必须返回布尔值");
            }
        } catch (Exception e) {
            return ValidationResult.invalid("脚本执行错误: " + e.getMessage());
        }
    }

    /**
     * 获取校验规则标识
     *
     * @return 规则ID
     */
    @Override
    public String getRule() {
        return ruleId;
    }

    /**
     * 获取或编译脚本（带缓存）
     *
     * @return 编译后的Groovy脚本对象
     */
    private Script getOrCompileScript() {
        return scriptCache.computeIfAbsent(ruleId, id -> sandbox.compileScript(scriptContent));
    }
}