package com.dataforge.security.validator;

import com.dataforge.security.SecurityPolicy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.regex.Pattern;

/**
 * 输入验证器
 * 防止恶意输入和注入攻击
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
public class InputValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(InputValidator.class);
    
    // SQL注入检测模式
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "('.+--)|(--)|(%7C)|(%27)|(%23)|(;)|(\\*)|(\\?)|(\\/)|(\\@)|(\\|)|(\\&)|(\\^)|(\\$)",
        Pattern.CASE_INSENSITIVE
    );
    
    // XSS攻击检测模式
    private static final Pattern XSS_PATTERN = Pattern.compile(
        "<script[^>]*>.*?</script>|<[^>]+on\\w+\\s*=|javascript:|vbscript:|onload\\s*=|onerror\\s*=",
        Pattern.CASE_INSENSITIVE
    );
    
    private final SecurityPolicy policy;
    
    public InputValidator(SecurityPolicy policy) {
        this.policy = policy;
    }
    
    /**
     * 验证字符串输入
     */
    public ValidationResult validateString(String input, String fieldName) {
        if (!policy.isEnableInputValidation()) {
            return ValidationResult.valid();
        }
        
        if (input == null) {
            return ValidationResult.valid();
        }
        
        // 长度检查
        if (input.length() > policy.getMaxStringLength()) {
            return ValidationResult.invalid(
                String.format("字段%s长度超过限制: %d > %d", 
                    fieldName, input.length(), policy.getMaxStringLength())
            );
        }
        
        // SQL注入检查
        if (policy.isEnableSqlInjectionCheck() && SQL_INJECTION_PATTERN.matcher(input).find()) {
            logger.warn("检测到SQL注入攻击: {}", input);
            return ValidationResult.invalid("输入包含潜在的SQL注入风险");
        }
        
        // XSS检查
        if (policy.isEnableXssProtection() && XSS_PATTERN.matcher(input).find()) {
            logger.warn("检测到XSS攻击: {}", input);
            return ValidationResult.invalid("输入包含潜在的XSS攻击风险");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * 验证集合输入
     */
    public ValidationResult validateCollection(Collection<?> collection, String fieldName) {
        if (!policy.isEnableInputValidation()) {
            return ValidationResult.valid();
        }
        
        if (collection == null) {
            return ValidationResult.valid();
        }
        
        if (collection.size() > policy.getMaxCollectionSize()) {
            return ValidationResult.invalid(
                String.format("集合%s大小超过限制: %d > %d", 
                    fieldName, collection.size(), policy.getMaxCollectionSize())
            );
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * 验证数字范围
     */
    public ValidationResult validateNumberRange(Number value, String fieldName, 
                                               Number min, Number max) {
        if (value == null) {
            return ValidationResult.valid();
        }
        
        double val = value.doubleValue();
        if (val < min.doubleValue() || val > max.doubleValue()) {
            return ValidationResult.invalid(
                String.format("字段%s值超出范围: %s, 期望范围[%s, %s]", 
                    fieldName, value, min, max)
            );
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * 验证文件路径（防止目录遍历）
     */
    public ValidationResult validateFilePath(String filePath, String fieldName) {
        if (StringUtils.isBlank(filePath)) {
            return ValidationResult.valid();
        }
        
        // 检查目录遍历攻击
        if (filePath.contains("..") || filePath.contains("~")) {
            return ValidationResult.invalid(
                String.format("文件路径%s包含非法字符", fieldName)
            );
        }
        
        // 检查绝对路径
        if (filePath.startsWith("/") || filePath.matches("^[A-Za-z]:.*")) {
            return ValidationResult.invalid(
                String.format("文件路径%s不允许使用绝对路径", fieldName)
            );
        }
        
        return ValidationResult.valid();
    }
}
