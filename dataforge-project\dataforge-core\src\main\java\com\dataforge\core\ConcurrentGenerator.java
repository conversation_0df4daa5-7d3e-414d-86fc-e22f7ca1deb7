package com.dataforge.core;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高性能并发数据生成器
 * 支持动态分片、进度监控和任务取消
 */
public class ConcurrentGenerator {
    private final ExecutorService executor;
    private final int parallelism;
    private volatile boolean isCancelled = false;
    private final DataGenerator dataGenerator;
    private final GeneratorContext context;
    
    public ConcurrentGenerator(DataGenerator dataGenerator, GeneratorContext context) {
        this.dataGenerator = dataGenerator;
        this.context = context;
        this.parallelism = Runtime.getRuntime().availableProcessors() * 2;
        this.executor = Executors.newWorkStealingPool(parallelism);
    }
    
    /**
     * 批量生成数据
     * @param count 生成数量
     * @param listener 进度监听器
     * @return 生成的数据列表
     */
    public List<Object> generateBatch(long count, ProgressListener listener)
        throws InterruptedException {
        
        isCancelled = false;
        List<CompletableFuture<List<Object>>> futures = new ArrayList<>();
        long itemsPerTask = calculateOptimalChunkSize(count);
        AtomicLong totalProcessed = new AtomicLong(0);
        
        for (long start = 0; start < count && !isCancelled; start += itemsPerTask) {
            final long chunkStart = start;
            long end = Math.min(start + itemsPerTask, count);
            futures.add(CompletableFuture.supplyAsync(() -> {
                List<Object> chunkResults = new ArrayList<>();
                for (long i = 0; i < (end - chunkStart); i++) {
                    if (isCancelled) break;
                    chunkResults.add(dataGenerator.generate(context));
                    long currentCount = totalProcessed.incrementAndGet();
                    if (listener != null && currentCount % 1000 == 0) {
                        listener.onProgress(currentCount, count);
                    }
                }
                return chunkResults;
            }, executor));
        }
        
        return futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }
    
    private long calculateOptimalChunkSize(long totalCount) {
        // 动态分片策略：每个任务处理1000-10000条数据
        long minChunk = 1000;
        long maxChunk = 10000;
        long chunk = totalCount / (parallelism * 4);
        
        return Math.max(minChunk, Math.min(chunk, maxChunk));
    }
    
    public void cancel() {
        isCancelled = true;
        executor.shutdownNow();
    }
    
    /**
     * 进度监听接口
     */
    @FunctionalInterface
    public interface ProgressListener {
        void onProgress(long processed, long total);
    }
}