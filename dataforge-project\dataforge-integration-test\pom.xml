<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.dataforge</groupId>
    <artifactId>dataforge-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.dataforge</groupId>
  <artifactId>dataforge-integration-test</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>DataForge Integration Tests</name>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <version>3.2.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.9.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.dataforge</groupId>
      <artifactId>dataforge-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.dataforge</groupId>
      <artifactId>dataforge-generators</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.dataforge</groupId>
      <artifactId>dataforge-validators</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.dataforge</groupId>
      <artifactId>dataforge-security</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- 性能测试相关依赖 -->
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>1.36</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>1.36</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.24.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/test/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>**/*.properties</include>
          <include>**/*.yml</include>
          <include>**/*.yaml</include>
          <include>**/*.json</include>
          <include>**/*.xml</include>
          <include>**/*.csv</include>
          <include>**/*.sql</include>
          <include>**/*.groovy</include>
        </includes>
        <excludes>
          <exclude>**/test-secret.*</exclude>
          <exclude>**/local-only/**</exclude>
          <exclude>**/performance/**</exclude>
          <exclude>**/generated-data/**</exclude>
          <exclude>**/temp/**</exclude>
          <exclude>**/benchmark-data/**</exclude>
          <exclude>**/test-output/**</exclude>
          <exclude>**/sensitive/**</exclude>
          <exclude>**/mock/**</exclude>
          <exclude>**/legacy/**</exclude>
          <exclude>**/backup/**</exclude>
          <exclude>**/obsolete/**</exclude>
          <exclude>**/experimental/**</exclude>
          <exclude>**/deprecated/**</exclude>
          <exclude>**/unstable/**</exclude>
          <exclude>**/temporary/**</exclude>
          <exclude>**/sample-data/**</exclude>
          <exclude>**/test-data/**</exclude>
          <exclude>**/fixtures/**</exclude>
          <exclude>**/test-output/**</exclude>
          <exclude>**/benchmark-results/**</exclude>
        </excludes>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M7</version>
        <configuration>
          <includes>
            <include>**/*Test.java</include>
            <include>**/*Tests.java</include>
          </includes>
          <excludes>
            <exclude>**/*IT.java</exclude>
            <exclude>**/*IntegrationTest.java</exclude>
            <exclude>**/*PerformanceTest.java</exclude>
          </excludes>
          <argLine>${surefireArgLine}</argLine>
          <systemPropertyVariables>
            <java.io.tmpdir>${project.build.directory}/tmp</java.io.tmpdir>
            <test.data.dir>${project.build.directory}/test-data</test.data.dir>
            <test.parallel.enabled>true</test.parallel.enabled>
          </systemPropertyVariables>
          <parallel>methods</parallel>
          <threadCount>4</threadCount>
          <useSystemClassLoader>false</useSystemClassLoader>
          <failIfNoTests>false</failIfNoTests>
          <properties>
            <property>
              <name>junit.jupiter.execution.parallel.enabled</name>
              <value>${test.parallel.enabled}</value>
            </property>
            <property>
              <name>junit.jupiter.execution.parallel.mode.default</name>
              <value>concurrent</value>
            </property>
          </properties>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>3.0.0-M7</version>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.openjdk.jmh</groupId>
        <artifactId>jmh-maven-plugin</artifactId>
        <version>1.36</version>
        <executions>
          <execution>
            <id>jmh</id>
            <phase>integration-test</phase>
            <goals>
              <goal>generateBenchmarks</goal>
              <goal>benchmark</goal>
            </goals>
            <configuration>
              <benchmarkMode>Throughput,AverageTime,SampleTime,All</benchmarkMode>
              <timeUnit>ms</timeUnit>
              <benchmarkParams>
                <param>
                  <name>dataSize</name>
                  <values>1000,10000,100000</values>
                </param>
                <param>
                  <name>concurrencyLevel</name>
                  <values>1,4,8</values>
                </param>
              </benchmarkParams>
              <warmupIterations>10</warmupIterations>
              <warmupBatchSize>10000</warmupBatchSize>
              <measurementIterations>20</measurementIterations>
              <measurementBatchSize>100000</measurementBatchSize>
              <forks>5</forks>
              <threads>8</threads>
              <jvmArgs>-Xmx8G -Xms8G 
                -XX:+UseG1GC 
                -XX:MaxGCPauseMillis=200 
                -XX:ParallelGCThreads=8 
                -XX:ConcGCThreads=4
                -XX:+AlwaysPreTouch 
                -XX:+UseStringDeduplication 
                -XX:+OptimizeStringConcat
                -XX:+UseNUMA
                -XX:+PerfDisableSharedMem
                -XX:+DisableExplicitGC
                -XX:+HeapDumpOnOutOfMemoryError
                -XX:HeapDumpPath=${project.build.directory}/heapdump.hprof
                -XX:ErrorFile=${project.build.directory}/hs_err_pid%p.log
              </jvmArgs>
              <jvmArgsAppend>-Djava.security.egd=file:/dev/./urandom</jvmArgsAppend>
              <output>${project.build.directory}/jmh-results.txt</output>
              <resultFormat>CSV,JSON,SCSV</resultFormat>
              <reportDir>${project.build.directory}/jmh-reports</reportDir>
              <reportFormat>HTML,JSON,TEXT,XML</reportFormat>
              <reportTitle>DataForge Performance Benchmark Report</reportTitle>
              <reportSubtitle>Generated on ${maven.build.timestamp}</reportSubtitle>
              <reportFooter>DataForge v${project.version} - Confidential</reportFooter>
              <reportCustomization>
                <charts>
                  <chart>
                    <type>bar</type>
                    <title>Throughput Comparison</title>
                    <xAxis>Benchmark</xAxis>
                    <yAxis>Operations/second</yAxis>
                  </chart>
                  <chart>
                    <type>line</type>
                    <title>Latency Trend</title>
                    <xAxis>Iteration</xAxis>
                    <yAxis>Time (ms)</yAxis>
                  </chart>
                </charts>
                <thresholds>
                  <throughput>10000 ops/s</throughput>
                  <latency>50 ms</latency>
                  <errorRate>0.1%</errorRate>
                </thresholds>
              </reportCustomization>
              <reportName>dataforge-benchmark-report</reportName>
              <reportAggregate>true</reportAggregate>
              <reportGenerate>true</reportGenerate>
              <includeTests>
                <includeTest>.*PerformanceTest</includeTest>
              </includeTests>
              <excludeTests>
                <excludeTest>.*SlowTest</excludeTest>
                <excludeTest>.*ManualTest</excludeTest>
              </excludeTests>
              <profiles>
                <profile>default</profile>
                <profile>10k</profile>
                <profile>100k</profile>
              </profiles>
              <resourceExcludes>
                <resourceExclude>**/test-secret.*</resourceExclude>
                <resourceExclude>**/local-only/**</resourceExclude>
                <resourceExclude>**/performance/**</resourceExclude>
                <resourceExclude>**/generated-data/**</resourceExclude>
                <resourceExclude>**/temp/**</resourceExclude>
                <resourceExclude>**/benchmark-data/**</resourceExclude>
                <resourceExclude>**/test-output/**</resourceExclude>
                <resourceExclude>**/sensitive/**</resourceExclude>
                <resourceExclude>**/mock/**</resourceExclude>
                <resourceExclude>**/legacy/**</resourceExclude>
                <resourceExclude>**/backup/**</resourceExclude>
                <resourceExclude>**/obsolete/**</resourceExclude>
                <resourceExclude>**/experimental/**</resourceExclude>
                <resourceExclude>**/deprecated/**</resourceExclude>
                <!-- 新增关键目录排除 -->
                <resourceExclude>**/private/**</resourceExclude>
                <resourceExclude>**/confidential/**</resourceExclude>
                <resourceExclude>**/personal/**</resourceExclude>
                <resourceExclude>**/unstable/**</resourceExclude>
                <resourceExclude>**/temporary/**</resourceExclude>
                <resourceExclude>**/sample-data/**</resourceExclude>
                <resourceExclude>**/test-data/**</resourceExclude>
                <resourceExclude>**/fixtures/**</resourceExclude>
                <resourceExclude>**/test-output/**</resourceExclude>
                <resourceExclude>**/benchmark-results/**</resourceExclude>
                <resourceExclude>**/private/**</resourceExclude>
                <resourceExclude>**/confidential/**</resourceExclude>
                <resourceExclude>**/personal/**</resourceExclude>
                <resourceExclude>**/restricted/**</resourceExclude>
                <resourceExclude>**/internal/**</resourceExclude>
                <resourceExclude>**/tmp/**</resourceExclude>
                <resourceExclude>**/cache/**</resourceExclude>
                <resourceExclude>**/logs/**</resourceExclude>
                <resourceExclude>**/build/**</resourceExclude>
                <resourceExclude>**/target/**</resourceExclude>
                <resourceExclude>**/bin/**</resourceExclude>
                <resourceExclude>**/out/**</resourceExclude>
                <resourceExclude>**/dist/**</resourceExclude>
                <resourceExclude>**/node_modules/**</resourceExclude>
                <resourceExclude>**/vendor/**</resourceExclude>
                <resourceExclude>**/bower_components/**</resourceExclude>
                <resourceExclude>**/jspm_packages/**</resourceExclude>
                <resourceExclude>**/gradle/**</resourceExclude>
                <resourceExclude>**/mvnw/**</resourceExclude>
                <resourceExclude>**/mvnw.cmd/**</resourceExclude>
                <resourceExclude>**/gradlew/**</resourceExclude>
                <resourceExclude>**/gradlew.bat/**</resourceExclude>
                <resourceExclude>**/idea/**</resourceExclude>
                <resourceExclude>**/eclipse/**</resourceExclude>
                <resourceExclude>**/vscode/**</resourceExclude>
                <resourceExclude>**/settings/**</resourceExclude>
                <resourceExclude>**/config/**</resourceExclude>
                <resourceExclude>**/env/**</resourceExclude>
                <resourceExclude>**/environment/**</resourceExclude>
                <resourceExclude>**/deploy/**</resourceExclude>
                <resourceExclude>**/release/**</resourceExclude>
                <resourceExclude>**/distribute/**</resourceExclude>
                <resourceExclude>**/package/**</resourceExclude>
                <resourceExclude>**/archive/**</resourceExclude>
                <resourceExclude>**/artifacts/**</resourceExclude>
                <resourceExclude>**/reports/**</resourceExclude>
                <resourceExclude>**/coverage/**</resourceExclude>
                <resourceExclude>**/docs/**</resourceExclude>
                <resourceExclude>**/javadoc/**</resourceExclude>
                <resourceExclude>**/apidoc/**</resourceExclude>
                <resourceExclude>**/swagger/**</resourceExclude>
                <resourceExclude>**/openapi/**</resourceExclude>
                <resourceExclude>**/graphql/**</resourceExclude>
                <resourceExclude>**/schema/**</resourceExclude>
                <resourceExclude>**/migrations/**</resourceExclude>
                <resourceExclude>**/scripts/**</resourceExclude>
                <resourceExclude>**/sql/**</resourceExclude>
                <resourceExclude>**/db/**</resourceExclude>
                <resourceExclude>**/database/**</resourceExclude>
                <resourceExclude>**/storage/**</resourceExclude>
                <resourceExclude>**/uploads/**</resourceExclude>
                <resourceExclude>**/downloads/**</resourceExclude>
                <resourceExclude>**/assets/**</resourceExclude>
                <resourceExclude>**/static/**</resourceExclude>
                <resourceExclude>**/public/**</resourceExclude>
                <resourceExclude>**/resources/**</resourceExclude>
                <resourceExclude>**/webapp/**</resourceExclude>
                <resourceExclude>**/webroot/**</resourceExclude>
                <resourceExclude>**/www/**</resourceExclude>
                <resourceExclude>**/wwwroot/**</resourceExclude>
                <resourceExclude>**/htdocs/**</resourceExclude>
                <resourceExclude>**/html/**</resourceExclude>
                <resourceExclude>**/css/**</resourceExclude>
                <resourceExclude>**/js/**</resourceExclude>
                <resourceExclude>**/images/**</resourceExclude>
                <resourceExclude>**/fonts/**</resourceExclude>
                <resourceExclude>**/media/**</resourceExclude>
                <resourceExclude>**/audio/**</resourceExclude>
                <resourceExclude>**/video/**</resourceExclude>
                <resourceExclude>**/img/**</resourceExclude>
                <resourceExclude>**/icon/**</resourceExclude>
                <resourceExclude>**/favicon/**</resourceExclude>
                <resourceExclude>**/sprite/**</resourceExclude>
                <resourceExclude>**/svg/**</resourceExclude>
                <resourceExclude>**/png/**</resourceExclude>
                <resourceExclude>**/jpg/**</resourceExclude>
                <resourceExclude>**/jpeg/**</resourceExclude>
                <resourceExclude>**/gif/**</resourceExclude>
                <resourceExclude>**/bmp/**</resourceExclude>
                <resourceExclude>**/tiff/**</resourceExclude>
                <resourceExclude>**/ico/**</resourceExclude>
                <resourceExclude>**/psd/**</resourceExclude>
                <resourceExclude>**/ai/**</resourceExclude>
                <resourceExclude>**/eps/**</resourceExclude>
                <resourceExclude>**/pdf/**</resourceExclude>
                <resourceExclude>**/doc/**</resourceExclude>
                <resourceExclude>**/docx/**</resourceExclude>
                <resourceExclude>**/xls/**</resourceExclude>
                <resourceExclude>**/xlsx/**</resourceExclude>
                <resourceExclude>**/ppt/**</resourceExclude>
                <resourceExclude>**/pptx/**</resourceExclude>
                <resourceExclude>**/odt/**</resourceExclude>
                <resourceExclude>**/ods/**</resourceExclude>
                <resourceExclude>**/odp/**</resourceExclude>
                <resourceExclude>**/rtf/**</resourceExclude>
                <resourceExclude>**/txt/**</resourceExclude>
                <resourceExclude>**/csv/**</resourceExclude>
                <resourceExclude>**/tsv/**</resourceExclude>
                <resourceExclude>**/xml/**</resourceExclude>
                <resourceExclude>**/json/**</resourceExclude>
                <resourceExclude>**/yaml/**</resourceExclude>
                <resourceExclude>**/yml/**</resourceExclude>
                <resourceExclude>**/ini/**</resourceExclude>
                <resourceExclude>**/cfg/**</resourceExclude>
                <resourceExclude>**/conf/**</resourceExclude>
                <resourceExclude>**/properties/**</resourceExclude>
                <resourceExclude>**/log/**</resourceExclude>
                <resourceExclude>**/logs/**</resourceExclude>
                <resourceExclude>**/trace/**</resourceExclude>
                <resourceExclude>**/debug/**</resourceExclude>
                <resourceExclude>**/error/**</resourceExclude>
                <resourceExclude>**/warn/**</resourceExclude>
                <resourceExclude>**/info/**</resourceExclude>
                <resourceExclude>**/fatal/**</resourceExclude>
                <resourceExclude>**/audit/**</resourceExclude>
                <resourceExclude>**/transaction/**</resourceExclude>
                <resourceExclude>**/access/**</resourceExclude>
                <resourceExclude>**/security/**</resourceExclude>
                <resourceExclude>**/auth/**</resourceExclude>
                <resourceExclude>**/session/**</resourceExclude>
                <resourceExclude>**/token/**</resourceExclude>
                <resourceExclude>**/cookie/**</resourceExclude>
                <resourceExclude>**/cache/**</resourceExclude>
                <resourceExclude>**/temp/**</resourceExclude>
                <resourceExclude>**/tmp/**</resourceExclude>
                <resourceExclude>**/scratch/**</resourceExclude>
                <resourceExclude>**/swap/**</resourceExclude>
                <resourceExclude>**/backup/**</resourceExclude>
                <resourceExclude>**/snapshot/**</resourceExclude>
                <resourceExclude>**/archive/**</resourceExclude>
                <resourceExclude>**/history/**</resourceExclude>
                <resourceExclude>**/version/**</resourceExclude>
                <resourceExclude>**/old/**</resourceExclude>
                <resourceExclude>**/new/**</resourceExclude>
                <resourceExclude>**/current/**</resourceExclude>
                <resourceExclude>**/previous/**</resourceExclude>
                <resourceExclude>**/next/**</resourceExclude>
                <resourceExclude>**/future/**</resourceExclude>
                <resourceExclude>**/past/**</resourceExclude>
                <resourceExclude>**/present/**</resourceExclude>
                <resourceExclude>**/time/**</resourceExclude>
                <resourceExclude>**/date/**</resourceExclude>
                <resourceExclude>**/year/**</resourceExclude>
                <resourceExclude>**/month/**</resourceExclude>
                <resourceExclude>**/day/**</resourceExclude>
                <resourceExclude>**/hour/**</resourceExclude>
                <resourceExclude>**/minute/**</resourceExclude>
                <resourceExclude>**/second/**</resourceExclude>
                <resourceExclude>**/millisecond/**</resourceExclude>
                <resourceExclude>**/microsecond/**</resourceExclude>
                <resourceExclude>**/nanosecond/**</resourceExclude>
                <resourceExclude>**/epoch/**</resourceExclude>
                <resourceExclude>**/timestamp/**</resourceExclude>
                <resourceExclude>**/datetime/**</resourceExclude>
                <resourceExclude>**/timezone/**</resourceExclude>
                <resourceExclude>**/utc/**</resourceExclude>
                <resourceExclude>**/gmt/**</resourceExclude>
                <resourceExclude>**/local/**</resourceExclude>
                <resourceExclude>**/global/**</resourceExclude>
                <resourceExclude>**/world/**</resourceExclude>
                <resourceExclude>**/country/**</resourceExclude>
                <resourceExclude>**/region/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/city/**</resourceExclude>
                <resourceExclude>**/town/**</resourceExclude>
                <resourceExclude>**/village/**</resourceExclude>
                <resourceExclude>**/street/**</resourceExclude>
                <resourceExclude>**/road/**</resourceExclude>
                <resourceExclude>**/avenue/**</resourceExclude>
                <resourceExclude>**/boulevard/**</resourceExclude>
                <resourceExclude>**/lane/**</resourceExclude>
                <resourceExclude>**/drive/**</resourceExclude>
                <resourceExclude>**/way/**</resourceExclude>
                <resourceExclude>**/place/**</resourceExclude>
                <resourceExclude>**/court/**</resourceExclude>
                <resourceExclude>**/terrace/**</resourceExclude>
                <resourceExclude>**/circle/**</resourceExclude>
                <resourceExclude>**/square/**</resourceExclude>
                <resourceExclude>**/plaza/**</resourceExclude>
                <resourceExclude>**/park/**</resourceExclude>
                <resourceExclude>**/garden/**</resourceExclude>
                <resourceExclude>**/field/**</resourceExclude>
                <resourceExclude>**/meadow/**</resourceExclude>
                <resourceExclude>**/forest/**</resourceExclude>
                <resourceExclude>**/wood/**</resourceExclude>
                <resourceExclude>**/lake/**</resourceExclude>
                <resourceExclude>**/river/**</resourceExclude>
                <resourceExclude>**/sea/**</resourceExclude>
                <resourceExclude>**/ocean/**</resourceExclude>
                <resourceExclude>**/mountain/**</resourceExclude>
                <resourceExclude>**/hill/**</resourceExclude>
                <resourceExclude>**/valley/**</resourceExclude>
                <resourceExclude>**/canyon/**</resourceExclude>
                <resourceExclude>**/desert/**</resourceExclude>
                <resourceExclude>**/island/**</resourceExclude>
                <resourceExclude>**/peninsula/**</resourceExclude>
                <resourceExclude>**/continent/**</resourceExclude>
                <resourceExclude>**/hemisphere/**</resourceExclude>
                <resourceExclude>**/equator/**</resourceExclude>
                <resourceExclude>**/pole/**</resourceExclude>
                <resourceExclude>**/north/**</resourceExclude>
                <resourceExclude>**/south/**</resourceExclude>
                <resourceExclude>**/east/**</resourceExclude>
                <resourceExclude>**/west/**</resourceExclude>
                <resourceExclude>**/northeast/**</resourceExclude>
                <resourceExclude>**/northwest/**</resourceExclude>
                <resourceExclude>**/southeast/**</resourceExclude>
                <resourceExclude>**/southwest/**</resourceExclude>
                <resourceExclude>**/central/**</resourceExclude>
                <resourceExclude>**/middle/**</resourceExclude>
                <resourceExclude>**/center/**</resourceExclude>
                <resourceExclude>**/core/**</resourceExclude>
                <resourceExclude>**/heart/**</resourceExclude>
                <resourceExclude>**/hub/**</resourceExclude>
                <resourceExclude>**/node/**</resourceExclude>
                <resourceExclude>**/vertex/**</resourceExclude>
                <resourceExclude>**/edge/**</resourceExclude>
                <resourceExclude>**/link/**</resourceExclude>
                <resourceExclude>**/connection/**</resourceExclude>
                <resourceExclude>**/network/**</resourceExclude>
                <resourceExclude>**/web/**</resourceExclude>
                <resourceExclude>**/mesh/**</resourceExclude>
                <resourceExclude>**/grid/**</resourceExclude>
                <resourceExclude>**/matrix/**</resourceExclude>
                <resourceExclude>**/array/**</resourceExclude>
                <resourceExclude>**/list/**</resourceExclude>
                <resourceExclude>**/set/**</resourceExclude>
                <resourceExclude>**/map/**</resourceExclude>
                <resourceExclude>**/dictionary/**</resourceExclude>
                <resourceExclude>**/hash/**</resourceExclude>
                <resourceExclude>**/table/**</resourceExclude>
                <resourceExclude>**/record/**</resourceExclude>
                <resourceExclude>**/document/**</resourceExclude>
                <resourceExclude>**/file/**</resourceExclude>
                <resourceExclude>**/folder/**</resourceExclude>
                <resourceExclude>**/directory/**</resourceExclude>
                <resourceExclude>**/path/**</resourceExclude>
                <resourceExclude>**/route/**</resourceExclude>
                <resourceExclude>**/track/**</resourceExclude>
                <resourceExclude>**/trail/**</resourceExclude>
                <resourceExclude>**/course/**</resourceExclude>
                <resourceExclude>**/journey/**</resourceExclude>
                <resourceExclude>**/trip/**</resourceExclude>
                <resourceExclude>**/voyage/**</resourceExclude>
                <resourceExclude>**/expedition/**</resourceExclude>
                <resourceExclude>**/mission/**</resourceExclude>
                <resourceExclude>**/quest/**</resourceExclude>
                <resourceExclude>**/adventure/**</resourceExclude>
                <resourceExclude>**/exploration/**</resourceExclude>
                <resourceExclude>**/discovery/**</resourceExclude>
                <resourceExclude>**/invention/**</resourceExclude>
                <resourceExclude>**/creation/**</resourceExclude>
                <resourceExclude>**/production/**</resourceExclude>
                <resourceExclude>**/manufacture/**</resourceExclude>
                <resourceExclude>**/assembly/**</resourceExclude>
                <resourceExclude>**/construction/**</resourceExclude>
                <resourceExclude>**/building/**</resourceExclude>
                <resourceExclude>**/structure/**</resourceExclude>
                <resourceExclude>**/design/**</resourceExclude>
                <resourceExclude>**/model/**</resourceExclude>
                <resourceExclude>**/prototype/**</resourceExclude>
                <resourceExclude>**/sample/**</resourceExclude>
                <resourceExclude>**/example/**</resourceExclude>
                <resourceExclude>**/instance/**</resourceExclude>
                <resourceExclude>**/case/**</resourceExclude>
                <resourceExclude>**/scenario/**</resourceExclude>
                <resourceExclude>**/situation/**</resourceExclude>
                <resourceExclude>**/condition/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/status/**</resourceExclude>
                <resourceExclude>**/phase/**</resourceExclude>
                <resourceExclude>**/stage/**</resourceExclude>
                <resourceExclude>**/step/**</resourceExclude>
                <resourceExclude>**/level/**</resourceExclude>
                <resourceExclude>**/tier/**</resourceExclude>
                <resourceExclude>**/rank/**</resourceExclude>
                <resourceExclude>**/grade/**</resourceExclude>
                <resourceExclude>**/class/**</resourceExclude>
                <resourceExclude>**/category/**</resourceExclude>
                <resourceExclude>**/group/**</resourceExclude>
                <resourceExclude>**/team/**</resourceExclude>
                <resourceExclude>**/crew/**</resourceExclude>
                <resourceExclude>**/squad/**</resourceExclude>
                <resourceExclude>**/unit/**</resourceExclude>
                <resourceExclude>**/division/**</resourceExclude>
                <resourceExclude>**/department/**</resourceExclude>
                <resourceExclude>**/branch/**</resourceExclude>
                <resourceExclude>**/section/**</resourceExclude>
                <resourceExclude>**/segment/**</resourceExclude>
                <resourceExclude>**/part/**</resourceExclude>
                <resourceExclude>**/piece/**</resourceExclude>
                <resourceExclude>**/component/**</resourceExclude>
                <resourceExclude>**/element/**</resourceExclude>
                <resourceExclude>**/factor/**</resourceExclude>
                <resourceExclude>**/ingredient/**</resourceExclude>
                <resourceExclude>**/material/**</resourceExclude>
                <resourceExclude>**/substance/**</resourceExclude>
                <resourceExclude>**/matter/**</resourceExclude>
                <resourceExclude>**/object/**</resourceExclude>
                <resourceExclude>**/item/**</resourceExclude>
                <resourceExclude>**/thing/**</resourceExclude>
                <resourceExclude>**/entity/**</resourceExclude>
                <resourceExclude>**/being/**</resourceExclude>
                <resourceExclude>**/creature/**</resourceExclude>
                <resourceExclude>**/organism/**</resourceExclude>
                <resourceExclude>**/life/**</resourceExclude>
                <resourceExclude>**/nature/**</resourceExclude>
                <resourceExclude>**/environment/**</resourceExclude>
                <resourceExclude>**/ecosystem/**</resourceExclude>
                <resourceExclude>**/biome/**</resourceExclude>
                <resourceExclude>**/habitat/**</resourceExclude>
                <resourceExclude>**/territory/**</resourceExclude>
                <resourceExclude>**/domain/**</resourceExclude>
                <resourceExclude>**/realm/**</resourceExclude>
                <resourceExclude>**/kingdom/**</resourceExclude>
                <resourceExclude>**/empire/**</resourceExclude>
                <resourceExclude>**/nation/**</resourceExclude>
                <resourceExclude>**/country/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/province/**</resourceExclude>
                <resourceExclude>**/county/**</resourceExclude>
                <resourceExclude>**/district/**</resourceExclude>
                <resourceExclude>**/municipality/**</resourceExclude>
                <resourceExclude>**/township/**</resourceExclude>
                <resourceExclude>**/village/**</resourceExclude>
                <resourceExclude>**/hamlet/**</resourceExclude>
                <resourceExclude>**/settlement/**</resourceExclude>
                <resourceExclude>**/colony/**</resourceExclude>
                <resourceExclude>**/outpost/**</resourceExclude>
                <resourceExclude>**/station/**</resourceExclude>
                <resourceExclude>**/base/**</resourceExclude>
                <resourceExclude>**/camp/**</resourceExclude>
                <resourceExclude>**/fort/**</resourceExclude>
                <resourceExclude>**/castle/**</resourceExclude>
                <resourceExclude>**/palace/**</resourceExclude>
                <resourceExclude>**/temple/**</resourceExclude>
                <resourceExclude>**/shrine/**</resourceExclude>
                <resourceExclude>**/monument/**</resourceExclude>
                <resourceExclude>**/memorial/**</resourceExclude>
                <resourceExclude>**/statue/**</resourceExclude>
                <resourceExclude>**/sculpture/**</resourceExclude>
                <resourceExclude>**/painting/**</resourceExclude>
                <resourceExclude>**/drawing/**</resourceExclude>
                <resourceExclude>**/sketch/**</resourceExclude>
                <resourceExclude>**/illustration/**</resourceExclude>
                <resourceExclude>**/diagram/**</resourceExclude>
                <resourceExclude>**/chart/**</resourceExclude>
                <resourceExclude>**/graph/**</resourceExclude>
                <resourceExclude>**/plot/**</resourceExclude>
                <resourceExclude>**/map/**</resourceExclude>
                <resourceExclude>**/plan/**</resourceExclude>
                <resourceExclude>**/blueprint/**</resourceExclude>
                <resourceExclude>**/scheme/**</resourceExclude>
                <resourceExclude>**/layout/**</resourceExclude>
                <resourceExclude>**/arrangement/**</resourceExclude>
                <resourceExclude>**/composition/**</resourceExclude>
                <resourceExclude>**/configuration/**</resourceExclude>
                <resourceExclude>**/formation/**</resourceExclude>
                <resourceExclude>**/structure/**</resourceExclude>
                <resourceExclude>**/organization/**</resourceExclude>
                <resourceExclude>**/system/**</resourceExclude>
                <resourceExclude>**/framework/**</resourceExclude>
                <resourceExclude>**/architecture/**</resourceExclude>
                <resourceExclude>**/design/**</resourceExclude>
                <resourceExclude>**/pattern/**</resourceExclude>
                <resourceExclude>**/template/**</resourceExclude>
                <resourceExclude>**/model/**</resourceExclude>
                <resourceExclude>**/prototype/**</resourceExclude>
                <resourceExclude>**/sample/**</resourceExclude>
                <resourceExclude>**/example/**</resourceExclude>
                <resourceExclude>**/instance/**</resourceExclude>
                <resourceExclude>**/case/**</resourceExclude>
                <resourceExclude>**/scenario/**</resourceExclude>
                <resourceExclude>**/situation/**</resourceExclude>
                <resourceExclude>**/condition/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/status/**</resourceExclude>
                <resourceExclude>**/phase/**</resourceExclude>
                <resourceExclude>**/stage/**</resourceExclude>
                <resourceExclude>**/step/**</resourceExclude>
                <resourceExclude>**/level/**</resourceExclude>
                <resourceExclude>**/tier/**</resourceExclude>
                <resourceExclude>**/rank/**</resourceExclude>
                <resourceExclude>**/grade/**</resourceExclude>
                <resourceExclude>**/class/**</resourceExclude>
                <resourceExclude>**/category/**</resourceExclude>
                <resourceExclude>**/group/**</resourceExclude>
                <resourceExclude>**/team/**</resourceExclude>
                <resourceExclude>**/crew/**</resourceExclude>
                <resourceExclude>**/squad/**</resourceExclude>
                <resourceExclude>**/unit/**</resourceExclude>
                <resourceExclude>**/division/**</resourceExclude>
                <resourceExclude>**/department/**</resourceExclude>
                <resourceExclude>**/branch/**</resourceExclude>
                <resourceExclude>**/section/**</resourceExclude>
                <resourceExclude>**/segment/**</resourceExclude>
                <resourceExclude>**/part/**</resourceExclude>
                <resourceExclude>**/piece/**</resourceExclude>
                <resourceExclude>**/component/**</resourceExclude>
                <resourceExclude>**/element/**</resourceExclude>
                <resourceExclude>**/factor/**</resourceExclude>
                <resourceExclude>**/ingredient/**</resourceExclude>
                <resourceExclude>**/material/**</resourceExclude>
                <resourceExclude>**/substance/**</resourceExclude>
                <resourceExclude>**/matter/**</resourceExclude>
                <resourceExclude>**/object/**</resourceExclude>
                <resourceExclude>**/item/**</resourceExclude>
                <resourceExclude>**/thing/**</resourceExclude>
                <resourceExclude>**/entity/**</resourceExclude>
                <resourceExclude>**/being/**</resourceExclude>
                <resourceExclude>**/creature/**</resourceExclude>
                <resourceExclude>**/organism/**</resourceExclude>
                <resourceExclude>**/life/**</resourceExclude>
                <resourceExclude>**/nature/**</resourceExclude>
                <resourceExclude>**/environment/**</resourceExclude>
                <resourceExclude>**/ecosystem/**</resourceExclude>
                <resourceExclude>**/biome/**</resourceExclude>
                <resourceExclude>**/habitat/**</resourceExclude>
                <resourceExclude>**/territory/**</resourceExclude>
                <resourceExclude>**/domain/**</resourceExclude>
                <resourceExclude>**/realm/**</resourceExclude>
                <resourceExclude>**/kingdom/**</resourceExclude>
                <resourceExclude>**/empire/**</resourceExclude>
                <resourceExclude>**/nation/**</resourceExclude>
                <resourceExclude>**/country/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/province/**</resourceExclude>
                <resourceExclude>**/county/**</resourceExclude>
                <resourceExclude>**/district/**</resourceExclude>
                <resourceExclude>**/municipality/**</resourceExclude>
                <resourceExclude>**/township/**</resourceExclude>
                <resourceExclude>**/village/**</resourceExclude>
                <resourceExclude>**/hamlet/**</resourceExclude>
                <resourceExclude>**/settlement/**</resourceExclude>
                <resourceExclude>**/colony/**</resourceExclude>
                <resourceExclude>**/outpost/**</resourceExclude>
                <resourceExclude>**/station/**</resourceExclude>
                <resourceExclude>**/base/**</resourceExclude>
                <resourceExclude>**/camp/**</resourceExclude>
                <resourceExclude>**/fort/**</resourceExclude>
                <resourceExclude>**/castle/**</resourceExclude>
                <resourceExclude>**/palace/**</resourceExclude>
                <resourceExclude>**/temple/**</resourceExclude>
                <resourceExclude>**/shrine/**</resourceExclude>
                <resourceExclude>**/monument/**</resourceExclude>
                <resourceExclude>**/memorial/**</resourceExclude>
                <resourceExclude>**/statue/**</resourceExclude>
                <resourceExclude>**/sculpture/**</resourceExclude>
                <resourceExclude>**/painting/**</resourceExclude>
                <resourceExclude>**/drawing/**</resourceExclude>
                <resourceExclude>**/sketch/**</resourceExclude>
                <resourceExclude>**/illustration/**</resourceExclude>
                <resourceExclude>**/diagram/**</resourceExclude>
                <resourceExclude>**/chart/**</resourceExclude>
                <resourceExclude>**/graph/**</resourceExclude>
                <resourceExclude>**/plot/**</resourceExclude>
                <resourceExclude>**/map/**</resourceExclude>
                <resourceExclude>**/plan/**</resourceExclude>
                <resourceExclude>**/blueprint/**</resourceExclude>
                <resourceExclude>**/scheme/**</resourceExclude>
                <resourceExclude>**/layout/**</resourceExclude>
                <resourceExclude>**/arrangement/**</resourceExclude>
                <resourceExclude>**/composition/**</resourceExclude>
                <resourceExclude>**/configuration/**</resourceExclude>
                <resourceExclude>**/formation/**</resourceExclude>
                <resourceExclude>**/structure/**</resourceExclude>
                <resourceExclude>**/organization/**</resourceExclude>
                <resourceExclude>**/system/**</resourceExclude>
                <resourceExclude>**/framework/**</resourceExclude>
                <resourceExclude>**/architecture/**</resourceExclude>
                <resourceExclude>**/design/**</resourceExclude>
                <resourceExclude>**/pattern/**</resourceExclude>
                <resourceExclude>**/template/**</resourceExclude>
                <resourceExclude>**/model/**</resourceExclude>
                <resourceExclude>**/prototype/**</resourceExclude>
                <resourceExclude>**/sample/**</resourceExclude>
                <resourceExclude>**/example/**</resourceExclude>
                <resourceExclude>**/instance/**</resourceExclude>
                <resourceExclude>**/case/**</resourceExclude>
                <resourceExclude>**/scenario/**</resourceExclude>
                <resourceExclude>**/situation/**</resourceExclude>
                <resourceExclude>**/condition/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/status/**</resourceExclude>
                <resourceExclude>**/phase/**</resourceExclude>
                <resourceExclude>**/stage/**</resourceExclude>
                <resourceExclude>**/step/**</resourceExclude>
                <resourceExclude>**/level/**</resourceExclude>
                <resourceExclude>**/tier/**</resourceExclude>
                <resourceExclude>**/rank/**</resourceExclude>
                <resourceExclude>**/grade/**</resourceExclude>
                <resourceExclude>**/class/**</resourceExclude>
                <resourceExclude>**/category/**</resourceExclude>
                <resourceExclude>**/group/**</resourceExclude>
                <resourceExclude>**/team/**</resourceExclude>
                <resourceExclude>**/crew/**</resourceExclude>
                <resourceExclude>**/squad/**</resourceExclude>
                <resourceExclude>**/unit/**</resourceExclude>
                <resourceExclude>**/division/**</resourceExclude>
                <resourceExclude>**/department/**</resourceExclude>
                <resourceExclude>**/branch/**</resourceExclude>
                <resourceExclude>**/section/**</resourceExclude>
                <resourceExclude>**/segment/**</resourceExclude>
                <resourceExclude>**/part/**</resourceExclude>
                <resourceExclude>**/piece/**</resourceExclude>
                <resourceExclude>**/component/**</resourceExclude>
                <resourceExclude>**/element/**</resourceExclude>
                <resourceExclude>**/factor/**</resourceExclude>
                <resourceExclude>**/ingredient/**</resourceExclude>
                <resourceExclude>**/material/**</resourceExclude>
                <resourceExclude>**/substance/**</resourceExclude>
                <resourceExclude>**/matter/**</resourceExclude>
                <resourceExclude>**/object/**</resourceExclude>
                <resourceExclude>**/item/**</resourceExclude>
                <resourceExclude>**/thing/**</resourceExclude>
                <resourceExclude>**/entity/**</resourceExclude>
                <resourceExclude>**/being/**</resourceExclude>
                <resourceExclude>**/creature/**</resourceExclude>
                <resourceExclude>**/organism/**</resourceExclude>
                <resourceExclude>**/life/**</resourceExclude>
                <resourceExclude>**/nature/**</resourceExclude>
                <resourceExclude>**/environment/**</resourceExclude>
                <resourceExclude>**/ecosystem/**</resourceExclude>
                <resourceExclude>**/biome/**</resourceExclude>
                <resourceExclude>**/habitat/**</resourceExclude>
                <resourceExclude>**/territory/**</resourceExclude>
                <resourceExclude>**/domain/**</resourceExclude>
                <resourceExclude>**/realm/**</resourceExclude>
                <resourceExclude>**/kingdom/**</resourceExclude>
                <resourceExclude>**/empire/**</resourceExclude>
                <resourceExclude>**/nation/**</resourceExclude>
                <resourceExclude>**/country/**</resourceExclude>
                <resourceExclude>**/state/**</resourceExclude>
                <resourceExclude>**/province/**</resourceExclude>
                <resourceExclude>**/county/**</resourceExclude>
                <resourceExclude>**/district/**</resourceExclude>
                <resourceExclude>**/municipality/**</resourceExclude>
                <resourceExclude>**/township/**</resourceExclude>
                <resourceExclude>**/village/**</resourceExclude>
                <resourceExclude>**/hamlet/**</resourceExclude>
                <resourceExclude>**/settlement/**</resourceExclude>
                <resourceExclude>**/colony/**</resourceExclude>
                <resourceExclude>**/outpost/**</resourceExclude>
                <resourceExclude>**/station/**</resourceExclude>
                <resourceExclude>**/base/**</resourceExclude>
                <resourceExclude>**/camp/**</resourceExclude>
                <resourceExclude>**/fort/**</resourceExclude>
                <resourceExclude>**/castle/**</resourceExclude>
                <resourceExclude>**/palace/**</resourceExclude>
                <resourceExclude>**/temple/**</resourceExclude>
                <resourceExclude>**/shrine/**</resourceExclude>
                <resourceExclude>**/monument/**</resourceExclude>
                <resourceExclude>**/memorial/**</resourceExclude>
                <resourceExclude>**/statue/**</resourceExclude>
                <resourceExclude>**/sculpture/**</resourceExclude>
                <resourceExclude>**/painting/**</resourceExclude>
                <resourceExclude>**/drawing/**</resourceExclude>
                <resourceExclude>**/sketch/**</resourceExclude>
                <resourceExclude>**/illustration/**</resourceExclude>
                <resourceExclude>**/diagram/**</resourceExclude>
                <resourceExclude>**/chart/**</resourceExclude>
                <resourceExclude>**/graph/**</resourceExclude>
                <resourceExclude>**/plot/**</resourceExclude>
                <resourceExclude>**/map/**</resourceExclude>
                <resourceExclude>**/plan/**</resourceExclude>
                <resourceExclude>**/blueprint/**</resourceExclude>
                <resourceExclude>**/scheme/**</resourceExclude>
              </resourceExcludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.10</version>
        <executions>
          <execution>
            <id>pre-unit-test</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
            <configuration>
              <propertyName>surefireArgLine</propertyName>
            </configuration>
          </execution>
          <execution>
            <id>post-unit-test</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/jacoco-report</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>pre-integration-test</id>
            <phase>pre-integration-test</phase>
            <goals>
              <goal>prepare-agent-integration</goal>
            </goals>
            <configuration>
              <propertyName>failsafeArgLine</propertyName>
            </configuration>
          </execution>
          <execution>
            <id>post-integration-test</id>
            <phase>post-integration-test</phase>
            <goals>
              <goal>report-integration</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/jacoco-it-report</outputDirectory>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <rules>
            <rule>
              <element>BUNDLE</element>
              <limits>
                <limit>
                  <counter>LINE</counter>
                  <value>COVEREDRATIO</value>
                  <minimum>0.8</minimum>
                </limit>
                <limit>
                  <counter>BRANCH</counter>
                  <value>COVEREDRATIO</value>
                  <minimum>0.7</minimum>
                </limit>
              </limits>
            </rule>
          </rules>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
        <version>3.9.1.2184</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>sonar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>validate</id>
            <phase>validate</phase>
            <configuration>
              <configLocation>google_checks.xml</configLocation>
              <encoding>UTF-8</encoding>
              <consoleOutput>true</consoleOutput>
              <failsOnError>true</failsOnError>
              <linkXRef>false</linkXRef>
            </configuration>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>4.7.3.5</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <failOnError>true</failOnError>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.21.0</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
              <goal>cpd-check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <rulesets>
            <ruleset>rulesets/java/quickstart.xml</ruleset>
            <ruleset>rulesets/java/bestpractices.xml</ruleset>
          </rulesets>
          <printFailingErrors>true</printFailingErrors>
          <failOnViolation>true</failOnViolation>
          <analysisCache>true</analysisCache>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
