package com.dataforge.core;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ServiceLoader;

public class GeneratorFactory {
    private final Map<String, DataGenerator<?>> generatorRegistry = new HashMap<>();
    
    public GeneratorFactory() {
        // 使用ServiceLoader自动发现所有DataGenerator实现
        ServiceLoader<DataGenerator> loader = ServiceLoader.load(DataGenerator.class);
        for (DataGenerator<?> generator : loader) {
            // 注册生成器支持的所有类型
            List<String> supportedTypes = getSupportedTypes(generator);
            for (String type : supportedTypes) {
                generatorRegistry.put(type.toLowerCase(), generator);
            }
        }
    }
    
    private List<String> getSupportedTypes(DataGenerator<?> generator) {
        List<String> types = new ArrayList<>();

        // 通过调用supports方法来获取支持的类型
        // 这里列出常见的数据类型进行检查
        String[] commonTypes = {
            "idcard", "id_card", "citizen_id",
            "phonenumber", "phone", "mobile",
            "bankcard", "creditcard", "card",
            "address", "location",
            "company", "enterprise",
            "network", "ip", "mac", "imei",
            "text", "string",
            "integer", "number", "int"
        };

        for (String type : commonTypes) {
            if (generator.supports(type)) {
                types.add(type);
            }
        }

        // 如果没有找到支持的类型，使用类名推断
        if (types.isEmpty()) {
            types.add(generator.getClass().getSimpleName().replace("Generator", "").toLowerCase());
        }

        return types;
    }
    
    @SuppressWarnings("unchecked")
    public <T> DataGenerator<T> getGenerator(String type) {
        DataGenerator<?> generator = generatorRegistry.get(type.toLowerCase());
        if (generator == null) {
            throw new IllegalArgumentException("No generator found for type: " + type);
        }
        return (DataGenerator<T>) generator;
    }
    
    public void registerGenerator(String type, DataGenerator<?> generator) {
        generatorRegistry.put(type.toLowerCase(), generator);
    }
}
