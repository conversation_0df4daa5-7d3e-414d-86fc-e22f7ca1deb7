package com.dataforge.core;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ServiceLoader;

public class GeneratorFactory {
    private final Map<String, DataGenerator<?>> generatorRegistry = new HashMap<>();
    
    public GeneratorFactory() {
        // 使用ServiceLoader自动发现所有DataGenerator实现
        ServiceLoader<DataGenerator> loader = ServiceLoader.load(DataGenerator.class);
        for (DataGenerator<?> generator : loader) {
            // 注册生成器支持的所有类型
            List<String> supportedTypes = getSupportedTypes(generator);
            for (String type : supportedTypes) {
                generatorRegistry.put(type.toLowerCase(), generator);
            }
        }
    }
    
    private List<String> getSupportedTypes(DataGenerator<?> generator) {
        // 实际实现中，生成器应该通过注解或方法声明支持的类型
        // 这里简化处理，假设每个生成器只支持一种类型
        List<String> types = new ArrayList<>();
        // 在实际项目中，这里可以解析生成器的注解或调用特定方法
        types.add(generator.getClass().getSimpleName().replace("Generator", "").toLowerCase());
        return types;
    }
    
    @SuppressWarnings("unchecked")
    public <T> DataGenerator<T> getGenerator(String type) {
        DataGenerator<?> generator = generatorRegistry.get(type.toLowerCase());
        if (generator == null) {
            throw new IllegalArgumentException("No generator found for type: " + type);
        }
        return (DataGenerator<T>) generator;
    }
    
    public void registerGenerator(String type, DataGenerator<?> generator) {
        generatorRegistry.put(type.toLowerCase(), generator);
    }
}
