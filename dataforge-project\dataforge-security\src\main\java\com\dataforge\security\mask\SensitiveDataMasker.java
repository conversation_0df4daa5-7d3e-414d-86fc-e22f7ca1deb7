package com.dataforge.security.mask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 敏感数据脱敏器
 * 对身份证号、银行卡号等敏感信息进行脱敏处理
 * 
 * <AUTHOR> Architect
 * @version 1.0
 */
public class SensitiveDataMasker {
    
    private static final Logger logger = LoggerFactory.getLogger(SensitiveDataMasker.class);
    
    // 身份证号脱敏模式：保留前6位和后4位
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("(\\d{6})\\d{8}(\\d{4})");
    
    // 银行卡号脱敏模式：保留前6位和后4位
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("(\\d{6})\\d+(\\d{4})");
    
    // 手机号脱敏模式：保留前3位和后4位
    private static final Pattern PHONE_PATTERN = Pattern.compile("(\\d{3})\\d{4}(\\d{4})");
    
    // 邮箱脱敏模式：保留前3位和域名
    private static final Pattern EMAIL_PATTERN = Pattern.compile("(.{3}).+(@.+)");
    
    // 姓名脱敏模式：保留姓
    private static final Pattern NAME_PATTERN = Pattern.compile("([\\u4e00-\\u9fa5]{1}).*");
    
    /**
     * 脱敏身份证号
     */
    public static String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 18) {
            return idCard;
        }
        return idCard.replaceAll(ID_CARD_PATTERN.pattern(), "$1********$2");
    }
    
    /**
     * 脱敏银行卡号
     */
    public static String maskBankCard(String bankCard) {
        if (bankCard == null || bankCard.length() < 10) {
            return bankCard;
        }
        return bankCard.replaceAll(BANK_CARD_PATTERN.pattern(), "$1****$2");
    }
    
    /**
     * 脱敏手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.replaceAll(PHONE_PATTERN.pattern(), "$1****$2");
    }
    
    /**
     * 脱敏邮箱
     */
    public static String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        return email.replaceAll(EMAIL_PATTERN.pattern(), "$1***$2");
    }
    
    /**
     * 脱敏姓名
     */
    public static String maskName(String name) {
        if (name == null || name.length() <= 1) {
            return name;
        }
        return name.replaceAll(NAME_PATTERN.pattern(), "$1*");
    }
    
    /**
     * 通用脱敏方法
     */
    public static String mask(String data, DataType type) {
        if (data == null || data.trim().isEmpty()) {
            return data;
        }
        
        try {
            switch (type) {
                case ID_CARD:
                    return maskIdCard(data);
                case BANK_CARD:
                    return maskBankCard(data);
                case PHONE:
                    return maskPhone(data);
                case EMAIL:
                    return maskEmail(data);
                case NAME:
                    return maskName(data);
                default:
                    // 未知类型，使用通用脱敏
                    return maskGeneric(data);
            }
        } catch (Exception e) {
            logger.error("脱敏处理失败: {}", e.getMessage());
            return "***"; // 脱敏失败时返回安全值
        }
    }
    
    /**
     * 通用脱敏：保留前3位和后3位
     */
    private static String maskGeneric(String data) {
        int length = data.length();
        if (length <= 6) {
            return "***";
        }
        
        String prefix = data.substring(0, 3);
        String suffix = data.substring(length - 3);
        return prefix + "***" + suffix;
    }
    
    /**
     * 敏感数据类型枚举
     */
    public enum DataType {
        ID_CARD,
        BANK_CARD,
        PHONE,
        EMAIL,
        NAME,
        GENERIC
    }
}
