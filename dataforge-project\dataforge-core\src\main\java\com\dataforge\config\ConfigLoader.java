package com.dataforge.config;

import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

public class ConfigLoader {
    
    public static GlobalConfig loadConfig(String configPath) throws FileNotFoundException {
        Yaml yaml = new Yaml(new Constructor(GlobalConfig.class));
        InputStream inputStream = new FileInputStream(configPath);
        return yaml.load(inputStream);
    }

    public static GlobalConfig loadDefaultConfig() {
        // 默认配置
        GlobalConfig config = new GlobalConfig();
        config.setOutputFormat("csv");
        config.setOutputPath("output");
        config.setBatchSize(1000);
        return config;
    }
}
