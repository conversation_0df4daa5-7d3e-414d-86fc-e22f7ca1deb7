package com.dataforge.core;

import java.util.Map;

public class GeneratorContext {
    private final Map<String, Object> config;
    private final long seed;
    
    public GeneratorContext(Map<String, Object> config, long seed) {
        this.config = config;
        this.seed = seed;
    }
    
    public <T> T getConfig(String key, Class<T> type) {
        return type.cast(config.get(key));
    }
    
    public long getSeed() {
        return seed;
    }
}
