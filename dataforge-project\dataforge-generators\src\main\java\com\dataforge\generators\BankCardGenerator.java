package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;
import java.util.Random;

public class BankCardGenerator implements DataGenerator<String> {

    // 常见银行卡号前缀（模拟）
    private static final String[] CARD_PREFIXES = {
        "622202", "622848", "622700", // 中国工商银行
        "622760", "622752", "622788", // 中国建设银行
        "622568", "622588", "622155", // 中国农业银行
        "622262", "622622", "622676", // 中国银行
        "622521", "622522", "622523", // 招商银行
        "622630", "622631", "622632"  // 民生银行
    };

    @Override
    public String generate(GeneratorContext context) {
        Random random = new Random(context.getSeed());
        
        // 1. 随机选择银行前缀
        String prefix = CARD_PREFIXES[random.nextInt(CARD_PREFIXES.length)];
        
        // 2. 生成中间号码（长度根据前缀不同而变化）
        int remainingLength = 16 - prefix.length() - 1; // 总长度16位，减前缀和校验位
        StringBuilder sb = new StringBuilder(prefix);
        for (int i = 0; i < remainingLength; i++) {
            sb.append(random.nextInt(10));
        }
        
        // 3. 使用Luhn算法计算校验位
        String partialCard = sb.toString();
        char checksum = calculateLuhnChecksum(partialCard);
        
        return partialCard + checksum;
    }

    private char calculateLuhnChecksum(String number) {
        int sum = 0;
        boolean alternate = true; // 从右向左处理，第一位（最后一位）是校验位，所以从倒数第二位开始
        
        for (int i = number.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(number.charAt(i));
            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit - 9; // 标准Luhn算法处理方式
                }
            }
            sum += digit;
            alternate = !alternate;
        }
        int checksum = (10 - (sum % 10)) % 10;
        return Character.forDigit(checksum, 10);
    }

    /**
     * 验证银行卡号是否通过Luhn校验
     * @param cardNumber 完整银行卡号
     * @return 校验通过返回true，否则false
     */
    public static boolean isValidLuhn(String cardNumber) {
        int sum = 0;
        boolean alternate = false; // 从右向左处理，包含校验位
        
        for (int i = cardNumber.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(cardNumber.charAt(i));
            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit - 9;
                }
            }
            sum += digit;
            alternate = !alternate;
        }
        return (sum % 10 == 0);
    }

    @Override
    public boolean supports(String type) {
        return "bankcard".equalsIgnoreCase(type);
    }
}
