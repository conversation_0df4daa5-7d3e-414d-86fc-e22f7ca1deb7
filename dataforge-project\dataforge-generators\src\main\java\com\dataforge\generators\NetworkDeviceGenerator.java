package com.dataforge.generators;

import com.dataforge.core.DataGenerator;
import com.dataforge.core.GeneratorContext;
import java.util.Random;

/**
 * 网络设备数据生成器，支持IP地址和MAC地址生成
 */
public class NetworkDeviceGenerator implements DataGenerator<String> {

    @Override
    public String generate(GeneratorContext context) {
        Random random = new Random(context.getSeed());
        String type = context.getConfig("network.type", String.class);
        if (type == null) {
            type = "any";
        }
        switch(type.toLowerCase()) {
            case "ip": return generateIpAddress(random);
            case "ipv6": return generateIpv6Address(random);
            case "mac": return generateMacAddress(random);
            case "imei": return generateImei(random);
            default: 
                // 随机生成任意类型
                int choice = random.nextInt(4);
                return switch(choice) {
                    case 0 -> generateIpAddress(random);
                    case 1 -> generateIpv6Address(random);
                    case 2 -> generateMacAddress(random);
                    case 3 -> generateImei(random);
                    default -> generateIpAddress(random);
                };
        }
    }

    @Override
    public boolean supports(String type) {
        return "network".equalsIgnoreCase(type) || 
               "ip".equalsIgnoreCase(type) ||
               "ipv6".equalsIgnoreCase(type) ||
               "mac".equalsIgnoreCase(type) ||
               "imei".equalsIgnoreCase(type) ||
               "imsi".equalsIgnoreCase(type);
    }

    /**
     * 生成随机IPv4地址
     */
    private String generateIpAddress(Random random) {
        return random.nextInt(256) + "." + random.nextInt(256) + "." + 
               random.nextInt(256) + "." + random.nextInt(256);
    }

    /**
     * 生成随机MAC地址
     */
    private String generateMacAddress(Random random) {
        byte[] macAddr = new byte[6];
        random.nextBytes(macAddr);
        // 设置本地管理的MAC地址标志位
        macAddr[0] = (byte)(macAddr[0] & (byte)254);  
        
        StringBuilder sb = new StringBuilder(18);
        for(byte b : macAddr){
            sb.append(String.format("%02x:", b));
        }
        return sb.substring(0, sb.length()-1).toUpperCase();
    }

    /**
     * 生成随机IPv6地址
     */
    private String generateIpv6Address(Random random) {
        return String.format("%04x:%04x:%04x:%04x:%04x:%04x:%04x:%04x",
            random.nextInt(65536), random.nextInt(65536),
            random.nextInt(65536), random.nextInt(65536),
            random.nextInt(65536), random.nextInt(65536),
            random.nextInt(65536), random.nextInt(65536));
    }

    /**
     * 生成随机IMEI号码(15位)
     */
    private String generateImei(Random random) {
        StringBuilder imei = new StringBuilder();
        int sum = 0;
        
        // 生成前14位
        for (int i = 0; i < 14; i++) {
            int digit = random.nextInt(10);
            imei.append(digit);
            
            // 计算Luhn校验和
            if ((i + 1) % 2 == 0) {
                digit *= 2;
                sum += digit > 9 ? digit - 9 : digit;
            } else {
                sum += digit;
            }
        }
        
        // 计算校验位
        int checkDigit = (10 - (sum % 10)) % 10;
        imei.append(checkDigit);
        
        return imei.toString();
    }
}
